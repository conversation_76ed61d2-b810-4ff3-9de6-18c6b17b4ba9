// Configure Monaco Editor environment before importing
if (typeof window !== 'undefined') {
    window.MonacoEnvironment = {
        getWorker: function (workerId, label) {
            // Create a mock worker that prevents the worker errors
            // but allows Monaco to function in main thread mode
            const worker = {
                postMessage: function(data) {
                    // Simulate async response with proper error handling
                    setTimeout(() => {
                        try {
                            if (this.onmessage) {
                                // Handle different types of Monaco Editor requests
                                let response = {
                                    id: data.id,
                                    result: null,
                                    error: null
                                };

                                // Handle specific Monaco Editor worker requests
                                if (data.method) {
                                    switch (data.method) {
                                        case 'initialize':
                                            response.result = { capabilities: {} };
                                            break;
                                        case 'textDocument/completion':
                                            response.result = { items: [] };
                                            break;
                                        case 'textDocument/hover':
                                            response.result = null;
                                            break;
                                        case 'textDocument/signatureHelp':
                                            response.result = null;
                                            break;
                                        default:
                                            response.result = null;
                                    }
                                }

                                this.onmessage({
                                    data: response
                                });
                            }
                        } catch (error) {
                            console.warn('Monaco worker mock error:', error);
                            if (this.onerror) {
                                this.onerror(error);
                            }
                        }
                    }, 0);
                },
                addEventListener: function(type, listener) {
                    if (type === 'message') {
                        this.onmessage = listener;
                    } else if (type === 'error') {
                        this.onerror = listener;
                    }
                },
                removeEventListener: function(type, listener) {
                    if (type === 'message' && this.onmessage === listener) {
                        this.onmessage = null;
                    } else if (type === 'error' && this.onerror === listener) {
                        this.onerror = null;
                    }
                },
                terminate: function() {
                    this.onmessage = null;
                    this.onerror = null;
                },
                onmessage: null,
                onerror: null
            };

            return worker;
        }
    };
}

// Check if we're running in Tauri or web mode
const isTauri = typeof window !== 'undefined' && window.__TAURI_IPC__;

// Initialize Tauri APIs or mocks
async function initializeTauriAPIs() {
    let invoke, appWindow, dialog;

    if (isTauri) {
        // Import Tauri APIs only if available
        const tauriModule = await import('@tauri-apps/api/tauri');
        const windowModule = await import('@tauri-apps/api/window');
        const dialogModule = await import('@tauri-apps/api');

        invoke = tauriModule.invoke;
        appWindow = windowModule.appWindow;
        dialog = dialogModule.dialog;
    } else {
        // Web-based APIs for online mode
        invoke = async (command, args) => {
            console.log(`Web invoke: ${command}`, args);
            return webTauriAPI(command, args);
        };

        appWindow = {
            minimize: () => console.log('Web: minimize'),
            toggleMaximize: () => console.log('Web: toggleMaximize'),
            close: () => console.log('Web: close')
        };

        dialog = {
            open: async (options) => {
                console.log('Web: dialog.open', options);
                // For web mode, always return root workspace
                return '/';
            }
        };
    }

    return { invoke, appWindow, dialog };
}
// Enhanced Web-based file system API
class WebFileSystem {
    constructor() {
        this.files = new Map();
        this.directories = new Map();
        this.fileCache = new Map();
        this.directoryCache = new Map();
        this.watchers = new Map();
        this.searchIndex = new Map();
        this.recentFiles = [];
        this.maxRecentFiles = 20;
        this.maxCacheSize = 100;
        this.compressionEnabled = true;
        this.encryptionEnabled = false;
        this.backupEnabled = true;
        this.maxBackups = 5;
        this.performanceMetrics = {
            readOperations: 0,
            writeOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalOperationTime: 0
        };
        this.eventListeners = new Map();
        this.loadFromStorage();
        this.initializeSearchIndex();
        this.startPerformanceMonitoring();
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('rustcode-filesystem');
            if (stored) {
                const data = JSON.parse(stored);
                this.files = new Map(data.files || []);
                this.directories = new Map(data.directories || []);
            }
        } catch (error) {
            console.warn('Failed to load from storage:', error);
        }
    }

    saveToStorage() {
        try {
            const data = {
                files: Array.from(this.files.entries()),
                directories: Array.from(this.directories.entries()),
                recentFiles: this.recentFiles,
                performanceMetrics: this.performanceMetrics
            };

            if (this.compressionEnabled) {
                // Simple compression simulation
                const compressed = JSON.stringify(data);
                localStorage.setItem('rustcode-filesystem', compressed);
            } else {
                localStorage.setItem('rustcode-filesystem', JSON.stringify(data));
            }

            // Create backup if enabled
            if (this.backupEnabled) {
                this.createBackup(data);
            }
        } catch (error) {
            console.warn('Failed to save to storage:', error);
        }
    }

    createBackup(data) {
        try {
            const backupKey = `rustcode-filesystem-backup-${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(data));

            // Clean old backups
            this.cleanOldBackups();
        } catch (error) {
            console.warn('Failed to create backup:', error);
        }
    }

    cleanOldBackups() {
        try {
            const backupKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('rustcode-filesystem-backup-')) {
                    backupKeys.push(key);
                }
            }

            // Sort by timestamp and keep only the most recent
            backupKeys.sort().reverse();

            if (backupKeys.length > this.maxBackups) {
                const toDelete = backupKeys.slice(this.maxBackups);
                toDelete.forEach(key => localStorage.removeItem(key));
            }
        } catch (error) {
            console.warn('Failed to clean old backups:', error);
        }
    }

    initializeSearchIndex() {
        // Build search index for faster file searching
        this.files.forEach((fileData, path) => {
            this.indexFile(path, fileData.content || '');
        });
    }

    indexFile(path, content) {
        // Simple search indexing
        const words = content.toLowerCase().split(/\W+/).filter(word => word.length > 2);
        const uniqueWords = [...new Set(words)];

        uniqueWords.forEach(word => {
            if (!this.searchIndex.has(word)) {
                this.searchIndex.set(word, new Set());
            }
            this.searchIndex.get(word).add(path);
        });
    }

    startPerformanceMonitoring() {
        // Monitor performance metrics
        setInterval(() => {
            this.optimizeCache();
            this.updatePerformanceMetrics();
        }, 30000); // Every 30 seconds
    }

    optimizeCache() {
        // Remove old cache entries if cache is too large
        if (this.fileCache.size > this.maxCacheSize) {
            const entries = Array.from(this.fileCache.entries());
            const toRemove = entries.slice(0, entries.length - this.maxCacheSize + 10);
            toRemove.forEach(([key]) => this.fileCache.delete(key));
        }

        if (this.directoryCache.size > this.maxCacheSize) {
            const entries = Array.from(this.directoryCache.entries());
            const toRemove = entries.slice(0, entries.length - this.maxCacheSize + 10);
            toRemove.forEach(([key]) => this.directoryCache.delete(key));
        }
    }

    updatePerformanceMetrics() {
        // Calculate cache hit ratio
        const totalOperations = this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses;
        const hitRatio = totalOperations > 0 ? this.performanceMetrics.cacheHits / totalOperations : 0;

        console.log(`File System Performance: ${(hitRatio * 100).toFixed(2)}% cache hit ratio`);
    }

    addEventListener(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    removeEventListener(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    async createFile(path, content = '') {
        this.files.set(path, {
            content,
            modified: new Date().toISOString(),
            size: content.length
        });
        this.saveToStorage();
        return true;
    }

    async readFile(path) {
        const file = this.files.get(path);
        return file ? file.content : '';
    }

    async writeFile(path, content) {
        const existing = this.files.get(path) || {};
        this.files.set(path, {
            ...existing,
            content,
            modified: new Date().toISOString(),
            size: content.length
        });
        this.saveToStorage();
        return true;
    }

    async deleteFile(path) {
        this.files.delete(path);
        this.saveToStorage();
        return true;
    }

    async createDirectory(path) {
        this.directories.set(path, {
            created: new Date().toISOString()
        });
        this.saveToStorage();
        return true;
    }

    getDirectoryTree(rootPath = '/') {
        const tree = {
            name: rootPath === '/' ? 'RustCode Workspace' : rootPath.split('/').pop(),
            path: rootPath,
            is_directory: true,
            children: []
        };

        // Add files and directories
        for (const [path, file] of this.files.entries()) {
            if (path.startsWith(rootPath) && path !== rootPath) {
                const relativePath = path.substring(rootPath.length);
                if (!relativePath.includes('/') || relativePath.indexOf('/') === relativePath.length - 1) {
                    tree.children.push({
                        name: path.split('/').pop(),
                        path: path,
                        is_directory: false,
                        size: file.size,
                        modified: file.modified
                    });
                }
            }
        }

        for (const [path] of this.directories.entries()) {
            if (path.startsWith(rootPath) && path !== rootPath) {
                const relativePath = path.substring(rootPath.length);
                if (!relativePath.includes('/') || relativePath.indexOf('/') === relativePath.length - 1) {
                    tree.children.push({
                        name: path.split('/').pop(),
                        path: path,
                        is_directory: true,
                        children: []
                    });
                }
            }
        }

        // Sort: directories first, then files
        tree.children.sort((a, b) => {
            if (a.is_directory && !b.is_directory) return -1;
            if (!a.is_directory && b.is_directory) return 1;
            return a.name.localeCompare(b.name);
        });

        return tree;
    }

    clearWorkspace() {
        this.files.clear();
        this.directories.clear();
        this.saveToStorage();
    }

    async moveFile(sourcePath, targetPath) {
        const sourceFile = this.files.get(sourcePath);
        if (sourceFile) {
            this.files.delete(sourcePath);
            this.files.set(targetPath, sourceFile);
            this.saveToStorage();
            return true;
        }

        const sourceDir = this.directories.get(sourcePath);
        if (sourceDir) {
            this.directories.delete(sourcePath);
            this.directories.set(targetPath, sourceDir);

            // Move all files and subdirectories
            const filesToMove = [];
            const dirsToMove = [];

            for (const [path, file] of this.files.entries()) {
                if (path.startsWith(sourcePath + '/')) {
                    filesToMove.push([path, file]);
                }
            }

            for (const [path, dir] of this.directories.entries()) {
                if (path.startsWith(sourcePath + '/')) {
                    dirsToMove.push([path, dir]);
                }
            }

            filesToMove.forEach(([oldPath, file]) => {
                const newPath = oldPath.replace(sourcePath, targetPath);
                this.files.delete(oldPath);
                this.files.set(newPath, file);
            });

            dirsToMove.forEach(([oldPath, dir]) => {
                const newPath = oldPath.replace(sourcePath, targetPath);
                this.directories.delete(oldPath);
                this.directories.set(newPath, dir);
            });

            this.saveToStorage();
            return true;
        }

        return false;
    }

    async copyFile(sourcePath, targetPath) {
        const sourceFile = this.files.get(sourcePath);
        if (sourceFile) {
            this.files.set(targetPath, {
                ...sourceFile,
                modified: new Date().toISOString()
            });
            this.saveToStorage();
            return true;
        }

        const sourceDir = this.directories.get(sourcePath);
        if (sourceDir) {
            this.directories.set(targetPath, {
                ...sourceDir,
                created: new Date().toISOString()
            });

            // Copy all files and subdirectories
            for (const [path, file] of this.files.entries()) {
                if (path.startsWith(sourcePath + '/')) {
                    const newPath = path.replace(sourcePath, targetPath);
                    this.files.set(newPath, {
                        ...file,
                        modified: new Date().toISOString()
                    });
                }
            }

            for (const [path, dir] of this.directories.entries()) {
                if (path.startsWith(sourcePath + '/')) {
                    const newPath = path.replace(sourcePath, targetPath);
                    this.directories.set(newPath, {
                        ...dir,
                        created: new Date().toISOString()
                    });
                }
            }

            this.saveToStorage();
            return true;
        }

        return false;
    }

    async renameFile(oldPath, newPath) {
        return this.moveFile(oldPath, newPath);
    }
}

// Editor state management
class EditorState {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = null;
        this.tabOrder = [];
        this.loadFromStorage();
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('rustcode-editor-state');
            if (stored) {
                const data = JSON.parse(stored);
                this.tabs = new Map(data.tabs || []);
                this.activeTabId = data.activeTabId;
                this.tabOrder = data.tabOrder || [];
            }
        } catch (error) {
            console.warn('Failed to load editor state:', error);
        }
    }

    saveToStorage() {
        try {
            const data = {
                tabs: Array.from(this.tabs.entries()),
                activeTabId: this.activeTabId,
                tabOrder: this.tabOrder
            };
            localStorage.setItem('rustcode-editor-state', JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save editor state:', error);
        }
    }

    addTab(filePath, content) {
        const tabId = 'tab-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const fileName = filePath.split('/').pop();
        const language = this.detectLanguage(fileName);

        const tab = {
            id: tabId,
            file_path: filePath,
            file_name: fileName,
            content: content,
            is_modified: false,
            language: language
        };

        this.tabs.set(tabId, tab);
        this.tabOrder.push(tabId);
        this.activeTabId = tabId;
        this.saveToStorage();
        return tabId;
    }

    closeTab(tabId) {
        this.tabs.delete(tabId);
        this.tabOrder = this.tabOrder.filter(id => id !== tabId);

        if (this.activeTabId === tabId) {
            this.activeTabId = this.tabOrder.length > 0 ? this.tabOrder[this.tabOrder.length - 1] : null;
        }

        this.saveToStorage();
        return true;
    }

    setActiveTab(tabId) {
        if (this.tabs.has(tabId)) {
            this.activeTabId = tabId;
            this.saveToStorage();
            return true;
        }
        return false;
    }

    updateTabContent(tabId, content) {
        const tab = this.tabs.get(tabId);
        if (tab) {
            tab.content = content;
            tab.is_modified = true;
            this.tabs.set(tabId, tab);
            this.saveToStorage();
            return true;
        }
        return false;
    }

    markTabSaved(tabId) {
        const tab = this.tabs.get(tabId);
        if (tab) {
            tab.is_modified = false;
            this.tabs.set(tabId, tab);
            this.saveToStorage();
            return true;
        }
        return false;
    }

    getState() {
        return {
            tabs: Object.fromEntries(this.tabs),
            active_tab_id: this.activeTabId,
            tab_order: this.tabOrder
        };
    }

    detectLanguage(fileName) {
        const ext = fileName.split('.').pop()?.toLowerCase();
        const languageMap = {
            'rs': 'rust',
            'js': 'javascript',
            'mjs': 'javascript',
            'ts': 'typescript',
            'py': 'python',
            'html': 'html',
            'htm': 'html',
            'css': 'css',
            'json': 'json',
            'xml': 'xml',
            'md': 'markdown',
            'yaml': 'yaml',
            'yml': 'yaml',
            'toml': 'toml',
            'sh': 'shell',
            'bash': 'shell'
        };
        return languageMap[ext] || 'plaintext';
    }
}

// Initialize web file system and editor state
const webFS = new WebFileSystem();
const editorState = new EditorState();

// Web-based API implementation
function webTauriAPI(command, args) {
    switch (command) {
        case 'get_directory_tree':
            return webFS.getDirectoryTree(args.path);
        case 'read_file_content':
            return webFS.readFile(args.path);
        case 'write_file_content':
            return webFS.writeFile(args.path, args.content);
        case 'create_file':
            return webFS.createFile(args.path, args.content || '');
        case 'create_directory':
            return webFS.createDirectory(args.path);
        case 'delete_file':
            return webFS.deleteFile(args.path);
        case 'open_file_in_editor':
            const content = webFS.readFile(args.path);
            return editorState.addTab(args.path, content);
        case 'close_editor_tab':
            return editorState.closeTab(args.tabId);
        case 'set_active_editor_tab':
            return editorState.setActiveTab(args.tabId);
        case 'update_editor_content':
            return editorState.updateTabContent(args.tabId, args.content);
        case 'save_editor_tab':
            const tab = editorState.tabs.get(args.tabId);
            if (tab) {
                webFS.writeFile(tab.file_path, tab.content);
                editorState.markTabSaved(args.tabId);
            }
            return true;
        case 'get_editor_state':
            return editorState.getState();
        case 'clear_workspace':
            webFS.clearWorkspace();
            editorState.tabs.clear();
            editorState.activeTabId = null;
            editorState.tabOrder = [];
            editorState.saveToStorage();
            return true;
        case 'move_file':
            return webFS.moveFile(args.source, args.target);
        case 'copy_file':
            return webFS.copyFile(args.source, args.target);
        case 'rename_file':
            return webFS.renameFile(args.oldPath, args.newPath);
        default:
            console.warn(`Web API: Unknown command ${command}`);
            return null;
    }
}



import * as monaco from 'monaco-editor';

import { FileExplorer } from './components/file-explorer.js';
import { EditorManager } from './components/editor.js';
import { TabManager } from './components/tabs.js';
import { StatusBar } from './components/status-bar.js';
import { CommandPalette } from './components/command-palette.js';
import { TerminalManager } from './components/terminal.js';
import { SearchManager } from './components/search.js';
import { SettingsManager } from './components/settings.js';
import { AIAssistant } from './components/ai-assistant.js';
import { GitIntegration } from './components/git-integration.js';
import { ProjectManager } from './components/project-manager.js';
import { DebugTestManager } from './components/debug-test-manager.js';

import { ErrorConsole } from './components/error-console.js';
import { createIcon, validateIcons } from './icons/icon-library.js';
import { monacoErrorHandler } from './utils/monaco-error-handler.js';

class RustCodeApp {
    constructor() {
        this.fileExplorer = null;
        this.editorManager = null;
        this.tabManager = null;
        this.statusBar = null;
        this.commandPalette = null;
        this.terminalManager = null;
        this.searchManager = null;
        this.settingsManager = null;
        this.aiAssistant = null;
        this.gitIntegration = null;
        this.projectManager = null;
        this.debugTestManager = null;

        this.errorConsole = null;
        this.currentWorkspace = null;
        this.invoke = null;
        this.appWindow = null;
        this.dialog = null;
        this.terminalVisible = false;
        this.searchVisible = false;
        this.aiVisible = false;
        this.gitVisible = false;
        this.projectVisible = false;
        this.debugTestVisible = false;


        this.init();
    }

    async init() {
        try {
            // Initialize Tauri APIs or mocks
            const apis = await initializeTauriAPIs();
            this.invoke = apis.invoke;
            this.appWindow = apis.appWindow;
            this.dialog = apis.dialog;

            // Make invoke function globally available for components
            window.rustCodeApp = this;

            // Make test method globally accessible for debugging
            window.testErrorConsole = () => this.testErrorConsole();

            // Initialize Monaco Editor
            await this.initializeMonaco();

            // Initialize components
            this.initializeComponents();

            // Set up event listeners
            this.setupEventListeners();

            // Set up keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Validate icons
            validateIcons();

            console.log('RustCode initialized successfully');
        } catch (error) {
            console.error('Failed to initialize RustCode:', error);
            this.showError('Failed to initialize application', error.message);
        }
    }



    async initializeMonaco() {
        try {
            // Ensure Monaco is available
            if (!monaco || !monaco.editor) {
                throw new Error('Monaco Editor is not available');
            }

            // Configure Monaco Editor with error handling
            try {
                monaco.editor.defineTheme('rustcode-dark', {
                    base: 'vs-dark',
                    inherit: true,
                    rules: [
                        { token: 'comment', foreground: '6A9955' },
                        { token: 'keyword', foreground: '569CD6' },
                        { token: 'string', foreground: 'CE9178' },
                        { token: 'number', foreground: 'B5CEA8' },
                        { token: 'type', foreground: '4EC9B0' },
                        { token: 'function', foreground: 'DCDCAA' },
                        { token: 'variable', foreground: '9CDCFE' },
                    ],
                    colors: {
                        'editor.background': '#1e1e1e',
                        'editor.foreground': '#cccccc',
                        'editor.lineHighlightBackground': '#2a2a2a',
                        'editor.selectionBackground': '#264f78',
                        'editor.inactiveSelectionBackground': '#3a3d41',
                        'editorLineNumber.foreground': '#858585',
                        'editorLineNumber.activeForeground': '#c6c6c6',
                        'editorCursor.foreground': '#ffffff',
                        'editor.findMatchBackground': '#515c6a',
                        'editor.findMatchHighlightBackground': '#ea5c004d',
                        'editor.wordHighlightBackground': '#575757b8',
                        'editor.wordHighlightStrongBackground': '#004972b8',
                        'editorBracketMatch.background': '#0064001a',
                        'editorBracketMatch.border': '#888888',
                    }
                });
            } catch (themeError) {
                console.warn('Failed to define custom theme, using default:', themeError);
            }

            try {
                monaco.editor.setTheme('rustcode-dark');
            } catch (setThemeError) {
                console.warn('Failed to set theme, using default:', setThemeError);
                // Fallback to default theme
                try {
                    monaco.editor.setTheme('vs-dark');
                } catch (fallbackThemeError) {
                    console.warn('Failed to set fallback theme:', fallbackThemeError);
                }
            }

            // Disable certain Monaco features that might cause factory errors
            try {
                // Disable language services that might cause issues
                if (monaco.languages && monaco.languages.typescript) {
                    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
                        noSemanticValidation: true,
                        noSyntaxValidation: false
                    });

                    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
                        noSemanticValidation: true,
                        noSyntaxValidation: false
                    });
                }
            } catch (languageError) {
                console.warn('Failed to configure language services:', languageError);
            }

            console.log('Monaco Editor initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Monaco Editor:', error);
            // Don't throw the error, allow the app to continue without Monaco
            this.showError('Monaco Editor initialization failed', error.message);
        }
    }

    initializeComponents() {
        // Initialize sidebar icons
        this.initializeSidebarIcons();

        // Initialize file explorer
        this.fileExplorer = new FileExplorer(
            document.getElementById('file-explorer'),
            this.onFileSelect.bind(this),
            this.onFileAction.bind(this)
        );

        // Initialize tab manager
        this.tabManager = new TabManager(
            document.getElementById('tab-bar'),
            this.onTabSelect.bind(this),
            this.onTabClose.bind(this)
        );

        // Initialize editor manager
        this.editorManager = new EditorManager(
            document.getElementById('monaco-editor'),
            this.onEditorChange.bind(this)
        );

        // Initialize status bar
        this.statusBar = new StatusBar(document.getElementById('status-bar'));

        // Initialize command palette
        this.commandPalette = new CommandPalette(
            document.getElementById('command-palette'),
            this.onCommandExecute.bind(this)
        );

        // Initialize terminal manager
        this.terminalManager = new TerminalManager(
            document.getElementById('terminal-panel')
        );

        // Initialize search manager
        this.searchManager = new SearchManager(
            document.getElementById('search-panel'),
            this.onSearchFileSelect.bind(this)
        );

        // Initialize settings manager
        this.settingsManager = new SettingsManager(
            document.querySelector('#settings-panel .settings-modal'),
            this.onSettingsChange.bind(this)
        );

        // Initialize AI assistant
        this.aiAssistant = new AIAssistant(
            document.getElementById('ai-assistant-panel'),
            this.editorManager
        );

        // Initialize Git integration
        this.gitIntegration = new GitIntegration(
            document.getElementById('git-panel'),
            this.fileExplorer
        );

        // Initialize Project Manager
        this.projectManager = new ProjectManager(
            document.getElementById('project-panel'),
            this.fileExplorer
        );

        // Initialize Debug Test Manager
        this.debugTestManager = new DebugTestManager(
            document.getElementById('debug-test-panel'),
            this.editorManager
        );

        // Initialize error console
        this.errorConsole = new ErrorConsole(
            document.getElementById('error-console-container')
        );

        // Make error console globally accessible
        window.errorConsole = this.errorConsole;

        // Make app globally accessible for system integration
        window.rustCodeApp = this;
    }

    initializeSidebarIcons() {
        // Populate sidebar action buttons with icons
        const newFileBtn = document.getElementById('new-file-btn');
        const newFolderBtn = document.getElementById('new-folder-btn');
        const refreshBtn = document.getElementById('refresh-btn');

        if (newFileBtn) {
            newFileBtn.innerHTML = createIcon('newFile', 16, 'sidebar-icon');
        }

        if (newFolderBtn) {
            newFolderBtn.innerHTML = createIcon('newFolder', 16, 'sidebar-icon');
        }

        if (refreshBtn) {
            refreshBtn.innerHTML = createIcon('refresh', 16, 'sidebar-icon');
        }
    }

    setupEventListeners() {
        // Helper function to safely add event listeners
        const safeAddEventListener = (id, event, handler) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener(event, handler);
            } else {
                console.warn(`Element with id '${id}' not found`);
            }
        };

        // Title bar controls
        safeAddEventListener('minimize-btn', 'click', () => {
            this.appWindow.minimize();
        });

        safeAddEventListener('maximize-btn', 'click', () => {
            this.appWindow.toggleMaximize();
        });

        safeAddEventListener('close-btn', 'click', () => {
            this.handleAppClose();
        });

        // Sidebar actions
        safeAddEventListener('new-file-btn', 'click', () => {
            this.createNewFile();
        });

        safeAddEventListener('new-folder-btn', 'click', () => {
            this.createNewFolder();
        });

        safeAddEventListener('refresh-btn', 'click', () => {
            this.refreshFileExplorer();
        });

        // Use event delegation for welcome screen buttons and other dynamic elements
        document.addEventListener('click', (e) => {
            switch (e.target.id) {
                case 'open-folder-btn':
                    this.openFolder();
                    break;
                case 'new-file-welcome-btn':
                    this.createNewFile();
                    break;
            }
        });

        // Resize handle
        this.setupResizeHandle();

        // Terminal resize handle
        this.setupTerminalResizeHandle();

        // Context menu
        this.setupContextMenu();

        // Menu bar
        this.setupMenuBar();
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Command palette
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                this.commandPalette.show();
            }

            // Quick open
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                this.commandPalette.show('files');
            }

            // Save file
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveCurrentFile();
            }

            // New file
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.createNewFile();
            }

            // Open folder
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'O') {
                e.preventDefault();
                this.openFolder();
            }

            // Close tab
            if ((e.ctrlKey || e.metaKey) && e.key === 'w') {
                e.preventDefault();
                this.closeCurrentTab();
            }

            // Find
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                this.editorManager.showFind();
            }

            // Find in files
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                this.toggleSearchPanel();
            }

            // Toggle terminal
            if ((e.ctrlKey || e.metaKey) && e.key === '`') {
                e.preventDefault();
                this.toggleTerminal();
            }

            // Format document
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                if (this.editorManager && this.editorManager.editor) {
                    this.editorManager.format();
                }
            }

            // Settings
            if ((e.ctrlKey || e.metaKey) && e.key === ',') {
                e.preventDefault();
                this.showSettings();
            }

            // AI Assistant
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
                e.preventDefault();
                this.toggleAIAssistant();
            }

            // Git Integration
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'G') {
                e.preventDefault();
                this.toggleGitIntegration();
            }

            // Project Manager
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                this.toggleProjectManager();
            }

            // Debug Test Manager
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggleDebugTestManager();
            }




        });
    }

    setupResizeHandle() {
        const resizeHandle = document.getElementById('resize-handle');
        const sidebar = document.getElementById('sidebar');
        let isResizing = false;

        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            const newWidth = e.clientX;
            if (newWidth >= 200 && newWidth <= 400) {
                sidebar.style.width = newWidth + 'px';
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    setupTerminalResizeHandle() {
        const terminalResizeHandle = document.getElementById('terminal-resize-handle');
        const terminalPanel = document.getElementById('terminal-panel');
        const editorContainer = document.getElementById('editor-container');
        let isResizing = false;

        terminalResizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.addEventListener('mousemove', handleTerminalResize);
            document.addEventListener('mouseup', stopTerminalResize);
        });

        const handleTerminalResize = (e) => {
            if (!isResizing) return;

            const containerRect = document.getElementById('editor-area').getBoundingClientRect();
            const newHeight = containerRect.bottom - e.clientY;

            if (newHeight >= 100 && newHeight <= containerRect.height * 0.8) {
                terminalPanel.style.height = newHeight + 'px';
                this.terminalManager.resize();
            }
        };

        const stopTerminalResize = () => {
            isResizing = false;
            document.removeEventListener('mousemove', handleTerminalResize);
            document.removeEventListener('mouseup', stopTerminalResize);
        };
    }

    setupContextMenu() {
        const contextMenu = document.getElementById('context-menu');

        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.hideContextMenu();
        });

        document.addEventListener('click', () => {
            this.hideContextMenu();
        });
    }

    setupMenuBar() {
        const menuItems = document.querySelectorAll('.menu-item');

        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                const menu = item.dataset.menu;
                this.handleMenuAction(menu);
            });
        });
    }

    handleMenuAction(menu) {
        switch (menu) {
            case 'file':
                // TODO: Implement file menu
                break;
            case 'edit':
                // TODO: Implement edit menu
                break;
            case 'view':
                // TODO: Implement view menu
                break;
            case 'search':
                this.toggleSearchPanel();
                break;
            case 'terminal':
                this.toggleTerminal();
                break;
            case 'errors':
                this.toggleErrorConsole();
                break;
            case 'ai':
                this.toggleAIAssistant();
                break;
            case 'git':
                this.toggleGitIntegration();
                break;
            case 'project':
                this.toggleProjectManager();
                break;
            case 'debug':
                this.toggleDebugTestManager();
                break;
            case 'settings':
                this.showSettings();
                break;
            case 'help':
                // TODO: Implement help menu
                break;
        }
    }





    toggleErrorConsole() {
        if (this.errorConsole) {
            this.errorConsole.toggle();
        }
    }

    toggleAIAssistant() {
        if (this.aiAssistant) {
            this.aiAssistant.toggle();
            this.aiVisible = this.aiAssistant.isVisible;
        }
    }

    toggleGitIntegration() {
        if (this.gitIntegration) {
            this.gitIntegration.toggle();
            this.gitVisible = this.gitIntegration.isVisible;
        }
    }

    toggleProjectManager() {
        if (this.projectManager) {
            this.projectManager.toggle();
            this.projectVisible = this.projectManager.isVisible;
        }
    }

    toggleDebugTestManager() {
        if (this.debugTestManager) {
            this.debugTestManager.toggle();
            this.debugTestVisible = this.debugTestManager.isVisible;
        }
    }

    // Test method to demonstrate error console functionality
    testErrorConsole() {
        if (this.errorConsole) {
            // Test different types of errors
            this.errorConsole.logError('Test runtime error', 'main.js', 123);
            this.errorConsole.logWarning('Test warning message', 'editor.js', 456);
            this.errorConsole.logInfo('Test info message', 'file-explorer.js', 789);

            // Test a JavaScript error
            setTimeout(() => {
                try {
                    // This will cause an error
                    nonExistentFunction();
                } catch (error) {
                    // This error will be caught by the error console
                    throw error;
                }
            }, 1000);

            console.warn('This is a test warning that should appear in the error console');
            console.error('This is a test error that should appear in the error console');
        }
    }

    showSettings() {
        document.getElementById('settings-panel').style.display = 'flex';
        this.settingsManager.show();
    }

    onSettingsChange(settings) {
        // Apply settings to editor
        if (this.editorManager) {
            this.settingsManager.applyEditorSettings();
        }

        // Apply settings to terminal
        if (this.terminalManager) {
            this.settingsManager.applyTerminalSettings();
        }

        console.log('Settings updated:', settings);
    }

    // Event handlers
    async onFileSelect(filePath) {
        try {
            const tabId = await this.invoke('open_file_in_editor', { path: filePath });
            const editorState = await this.invoke('get_editor_state');

            this.tabManager.updateTabs(editorState.tabs, editorState.tab_order, editorState.active_tab_id);

            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
                this.showEditor();
            }
        } catch (error) {
            console.error('Failed to open file:', error);
            this.showError('Failed to open file', error);
        }
    }

    async onFileAction(action, filePath) {
        try {
            switch (action) {
                case 'delete':
                    await this.invoke('delete_file', { path: filePath });
                    this.refreshFileExplorer();
                    break;
                case 'rename':
                    // TODO: Implement rename dialog
                    break;
                case 'new-file':
                    // TODO: Implement new file dialog
                    break;
                case 'new-folder':
                    // TODO: Implement new folder dialog
                    break;
            }
        } catch (error) {
            console.error('File action failed:', error);
            this.showError('File operation failed', error);
        }
    }

    async onTabSelect(tabId) {
        try {
            await this.invoke('set_active_editor_tab', { tabId });
            const editorState = await this.invoke('get_editor_state');

            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
            }
        } catch (error) {
            console.error('Failed to switch tab:', error);
        }
    }

    async onTabClose(tabId) {
        try {
            await this.invoke('close_editor_tab', { tabId });
            const editorState = await this.invoke('get_editor_state');

            this.tabManager.updateTabs(editorState.tabs, editorState.tab_order, editorState.active_tab_id);

            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
            } else {
                this.showWelcomeScreen();
            }
        } catch (error) {
            console.error('Failed to close tab:', error);
        }
    }

    async onEditorChange(content) {
        try {
            const editorState = await this.invoke('get_editor_state');
            if (editorState.active_tab_id) {
                await this.invoke('update_editor_content', {
                    tabId: editorState.active_tab_id,
                    content
                });

                // Update tab to show modified state
                const updatedState = await this.invoke('get_editor_state');
                this.tabManager.updateTabs(updatedState.tabs, updatedState.tab_order, updatedState.active_tab_id);
            }
        } catch (error) {
            console.error('Failed to update editor content:', error);
        }
    }

    async onCommandExecute(command) {
        try {
            switch (command.id) {
                case 'file.open':
                    await this.openFolder();
                    break;
                case 'file.new':
                    await this.createNewFile();
                    break;
                case 'file.save':
                    await this.saveCurrentFile();
                    break;
                case 'editor.find':
                    this.editorManager.showFind();
                    break;
                case 'workbench.action.findInFiles':
                    this.showSearchPanel();
                    break;
                default:
                    console.log('Unknown command:', command);
            }
        } catch (error) {
            console.error('Command execution failed:', error);
            this.showError('Command failed', error);
        }
    }

    // Utility methods
    async openFolder() {
        try {
            // For web mode, automatically create a clean workspace
            if (!isTauri) {
                // Clear existing workspace and tabs
                await this.invoke('clear_workspace');
                this.tabManager.updateTabs({}, [], null);
                this.showWelcomeScreen();

                this.currentWorkspace = '/';
                await this.fileExplorer.loadDirectory('/');
                this.statusBar.updateWorkspace('RustCode Workspace');
                return;
            }

            // For Tauri mode, show folder dialog
            const selected = await this.dialog.open({
                directory: true,
                multiple: false,
                title: 'Open Folder'
            });

            if (selected) {
                this.currentWorkspace = selected;
                await this.fileExplorer.loadDirectory(selected);
                this.statusBar.updateWorkspace(selected);
            }
        } catch (error) {
            console.error('Failed to open folder:', error);
            this.showError('Failed to open folder', error);
        }
    }

    async createNewFile() {
        const fileName = prompt('Enter file name:');
        if (fileName) {
            const filePath = `/${fileName}`;
            try {
                await this.invoke('create_file', { path: filePath });
                await this.refreshFileExplorer();

                // Open the new file in editor
                const tabId = await this.invoke('open_file_in_editor', { path: filePath });
                const editorState = await this.invoke('get_editor_state');

                this.tabManager.updateTabs(editorState.tabs, editorState.tab_order, editorState.active_tab_id);

                if (editorState.active_tab_id) {
                    const activeTab = editorState.tabs[editorState.active_tab_id];
                    this.editorManager.openFile(activeTab.content, activeTab.language);
                    this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
                    this.showEditor();
                }
            } catch (error) {
                console.error('Failed to create file:', error);
                this.showError('Failed to create file', error);
            }
        }
    }

    async createNewFolder() {
        const folderName = prompt('Enter folder name:');
        if (folderName) {
            const folderPath = `/${folderName}`;
            try {
                await this.invoke('create_directory', { path: folderPath });
                await this.refreshFileExplorer();
            } catch (error) {
                console.error('Failed to create folder:', error);
                this.showError('Failed to create folder', error);
            }
        }
    }

    async saveCurrentFile() {
        try {
            const editorState = await this.invoke('get_editor_state');
            if (editorState.active_tab_id) {
                await this.invoke('save_editor_tab', { tabId: editorState.active_tab_id });

                // Update tab to remove modified state
                const updatedState = await this.invoke('get_editor_state');
                this.tabManager.updateTabs(updatedState.tabs, updatedState.tab_order, updatedState.active_tab_id);

                this.statusBar.showNotification('File saved', 'success');
            }
        } catch (error) {
            console.error('Failed to save file:', error);
            this.showError('Failed to save file', error);
        }
    }

    async closeCurrentTab() {
        const editorState = await this.invoke('get_editor_state');
        if (editorState.active_tab_id) {
            await this.onTabClose(editorState.active_tab_id);
        }
    }

    async refreshFileExplorer() {
        if (this.currentWorkspace) {
            await this.fileExplorer.loadDirectory(this.currentWorkspace);
        }
    }

    showEditor() {
        document.getElementById('welcome-screen').style.display = 'none';
        document.getElementById('monaco-editor').style.display = 'block';
    }

    showWelcomeScreen() {
        document.getElementById('welcome-screen').style.display = 'flex';
        document.getElementById('monaco-editor').style.display = 'none';
    }

    toggleSearchPanel() {
        const searchPanel = document.getElementById('search-panel');
        const searchResizeHandle = document.getElementById('search-resize-handle');

        if (this.searchVisible) {
            // Hide search panel
            searchPanel.style.display = 'none';
            searchResizeHandle.style.display = 'none';
            this.searchVisible = false;
        } else {
            // Show search panel
            searchPanel.style.display = 'flex';
            searchResizeHandle.style.display = 'block';
            this.searchVisible = true;
            this.searchManager.show();
        }
    }

    async onSearchFileSelect(filePath, line = null, column = null) {
        try {
            // Open file in editor
            const tabId = await this.invoke('open_file_in_editor', { path: filePath });
            const editorState = await this.invoke('get_editor_state');

            this.tabManager.updateTabs(editorState.tabs, editorState.tab_order, editorState.active_tab_id);

            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
                this.showEditor();

                // Navigate to specific line and column if provided
                if (line !== null && this.editorManager.editor) {
                    setTimeout(() => {
                        this.editorManager.editor.setPosition({
                            lineNumber: line,
                            column: column || 1
                        });
                        this.editorManager.editor.revealLineInCenter(line);
                        this.editorManager.editor.focus();
                    }, 100);
                }
            }
        } catch (error) {
            console.error('Failed to open search result:', error);
            this.showError('Failed to open file', error);
        }
    }

    toggleTerminal() {
        const terminalPanel = document.getElementById('terminal-panel');
        const terminalResizeHandle = document.getElementById('terminal-resize-handle');
        const editorContainer = document.getElementById('editor-container');

        if (this.terminalVisible) {
            // Hide terminal
            terminalPanel.style.display = 'none';
            terminalResizeHandle.style.display = 'none';
            editorContainer.style.height = '100%';
            this.terminalVisible = false;
        } else {
            // Show terminal
            terminalPanel.style.display = 'flex';
            terminalResizeHandle.style.display = 'block';
            editorContainer.style.height = 'calc(100% - 300px)';
            this.terminalVisible = true;

            // Resize terminal to fit
            setTimeout(() => {
                this.terminalManager.resize();
            }, 100);
        }

        // Resize editor
        if (this.editorManager && this.editorManager.editor) {
            this.editorManager.editor.layout();
        }
    }

    hideContextMenu() {
        document.getElementById('context-menu').classList.add('hidden');
    }

    showError(title, message) {
        console.error(`${title}: ${message}`);
        this.statusBar.showNotification(`${title}: ${message}`, 'error');

        // Also log to error console
        if (this.errorConsole) {
            this.errorConsole.logError(`${title}: ${message}`);
        }
    }

    async handleAppClose() {
        try {
            const editorState = await this.invoke('get_editor_state');
            if (editorState && Object.values(editorState.tabs).some(tab => tab.is_modified)) {
                // TODO: Show unsaved changes dialog
                console.log('There are unsaved changes');
            }
            this.appWindow.close();
        } catch (error) {
            console.error('Error during app close:', error);
            this.appWindow.close();
        }
    }
}

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error caught:', event.error);
    if (window.errorConsole) {
        window.errorConsole.logError(event.error.message, event.filename, event.lineno);
    }
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    if (window.errorConsole) {
        window.errorConsole.logError(`Unhandled Promise Rejection: ${event.reason}`, 'Promise', 0);
    }
});

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        new RustCodeApp();
    } catch (error) {
        console.error('Failed to initialize RustCode:', error);
        // Show a basic error message if the app fails to initialize
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; background: #1e1e1e; color: #fff; font-family: monospace;">
                <div style="text-align: center;">
                    <h1>RustCode Initialization Error</h1>
                    <p>Failed to initialize the application: ${error.message}</p>
                    <p>Please refresh the page to try again.</p>
                    <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #007acc; color: white; border: none; border-radius: 4px; cursor: pointer;">Refresh</button>
                </div>
            </div>
        `;
    }
});
