import { createIcon } from '../icons/icon-library.js';

export class ProjectManager {
    constructor(container, fileExplorer) {
        this.container = container;
        this.fileExplorer = fileExplorer;
        this.isVisible = false;
        this.currentProject = null;
        this.projects = [];
        this.templates = [];
        this.dependencies = new Map();
        this.buildTasks = [];
        this.environments = ['development', 'staging', 'production'];
        this.currentEnvironment = 'development';
        this.projectTypes = ['web', 'desktop', 'mobile', 'library', 'cli', 'api'];
        this.buildSystems = ['npm', 'yarn', 'pnpm', 'cargo', 'maven', 'gradle', 'make', 'cmake'];
        this.currentBuildSystem = 'npm';
        this.projectMetrics = {
            totalFiles: 0,
            totalLines: 0,
            codeFiles: 0,
            testFiles: 0,
            lastBuild: null,
            buildDuration: 0,
            testCoverage: 0
        };
        this.deploymentTargets = [];
        this.cicdPipelines = [];
        this.projectSettings = {
            autoSave: true,
            autoFormat: true,
            enableLinting: true,
            enableTesting: true,
            enableDeployment: false,
            enableMonitoring: false,
            enableAdvancedFeatures: true
        };
        this.performanceMetrics = {
            buildTimes: [],
            testTimes: [],
            deploymentTimes: [],
            lastAnalysis: null
        };
        this.eventListeners = [];
        this.watchers = new Map();
        this.activeProcesses = new Map();

        this.init();
    }

    init() {
        this.loadSettings();
        this.createUI();
        this.setupEventListeners();
        this.loadProjectData();
        this.initializeTemplates();
        this.initializeBuildSystems();
        this.setupProjectWatchers();
        this.startPerformanceMonitoring();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="project-panel">
                <div class="project-header">
                    <div class="project-title">
                        ${createIcon('project', 16)} Project Manager
                    </div>
                    <div class="project-controls">
                        <button class="project-btn" id="new-project-btn" title="New Project">
                            ${createIcon('plus', 14)}
                        </button>
                        <button class="project-btn" id="open-project-btn" title="Open Project">
                            ${createIcon('folder', 14)}
                        </button>
                        <button class="project-btn" id="project-settings-btn" title="Project Settings">
                            ${createIcon('settings', 14)}
                        </button>
                        <button class="project-btn" id="project-close-btn" title="Close">
                            ${createIcon('close', 14)}
                        </button>
                    </div>
                </div>

                <div class="project-info" id="project-info">
                    <div class="current-project" id="current-project">
                        <span class="project-name">No project loaded</span>
                        <span class="project-path"></span>
                    </div>
                    <div class="environment-selector">
                        <label for="environment-select">Environment:</label>
                        <select id="environment-select">
                            <option value="development">Development</option>
                            <option value="staging">Staging</option>
                            <option value="production">Production</option>
                        </select>
                    </div>
                </div>

                <div class="project-tabs">
                    <button class="project-tab active" data-tab="overview">Overview</button>
                    <button class="project-tab" data-tab="dependencies">Dependencies</button>
                    <button class="project-tab" data-tab="build">Build</button>
                    <button class="project-tab" data-tab="templates">Templates</button>
                </div>

                <div class="project-tab-content">
                    <!-- Overview Tab -->
                    <div class="tab-panel active" id="overview-panel">
                        <div class="project-stats">
                            <div class="stat-card">
                                <div class="stat-value" id="file-count">0</div>
                                <div class="stat-label">Files</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="line-count">0</div>
                                <div class="stat-label">Lines of Code</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="dependency-count">0</div>
                                <div class="stat-label">Dependencies</div>
                            </div>
                        </div>

                        <div class="project-actions">
                            <button class="action-btn primary" id="run-project-btn">
                                ${createIcon('play', 14)} Run Project
                            </button>
                            <button class="action-btn" id="build-project-btn">
                                ${createIcon('build', 14)} Build
                            </button>
                            <button class="action-btn" id="test-project-btn">
                                ${createIcon('test', 14)} Test
                            </button>
                            <button class="action-btn" id="deploy-project-btn">
                                ${createIcon('deploy', 14)} Deploy
                            </button>
                        </div>

                        <div class="recent-projects">
                            <h3>Recent Projects</h3>
                            <div class="recent-list" id="recent-projects-list">
                                <!-- Recent projects will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Dependencies Tab -->
                    <div class="tab-panel" id="dependencies-panel">
                        <div class="dependencies-header">
                            <h3>Dependencies</h3>
                            <div class="dependency-actions">
                                <button class="action-btn" id="add-dependency-btn">
                                    ${createIcon('plus', 14)} Add Dependency
                                </button>
                                <button class="action-btn" id="update-dependencies-btn">
                                    ${createIcon('refresh', 14)} Update All
                                </button>
                                <button class="action-btn" id="audit-dependencies-btn">
                                    ${createIcon('shield', 14)} Security Audit
                                </button>
                            </div>
                        </div>

                        <div class="dependency-categories">
                            <div class="dependency-category">
                                <h4>Production Dependencies</h4>
                                <div class="dependency-list" id="prod-dependencies">
                                    <!-- Production dependencies will be populated here -->
                                </div>
                            </div>

                            <div class="dependency-category">
                                <h4>Development Dependencies</h4>
                                <div class="dependency-list" id="dev-dependencies">
                                    <!-- Development dependencies will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Build Tab -->
                    <div class="tab-panel" id="build-panel">
                        <div class="build-header">
                            <h3>Build Configuration</h3>
                            <div class="build-actions">
                                <button class="action-btn" id="add-build-task-btn">
                                    ${createIcon('plus', 14)} Add Task
                                </button>
                                <button class="action-btn" id="run-build-btn">
                                    ${createIcon('play', 14)} Run Build
                                </button>
                            </div>
                        </div>

                        <div class="build-tasks" id="build-tasks">
                            <!-- Build tasks will be populated here -->
                        </div>

                        <div class="build-output">
                            <h4>Build Output</h4>
                            <div class="output-console" id="build-output-console">
                                <div class="console-line">Ready to build...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Tab -->
                    <div class="tab-panel" id="templates-panel">
                        <div class="templates-header">
                            <h3>Project Templates</h3>
                            <div class="template-actions">
                                <button class="action-btn" id="create-template-btn">
                                    ${createIcon('plus', 14)} Create Template
                                </button>
                                <button class="action-btn" id="import-template-btn">
                                    ${createIcon('download', 14)} Import Template
                                </button>
                            </div>
                        </div>

                        <div class="template-grid" id="template-grid">
                            <!-- Templates will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- New Project Modal -->
                <div class="project-modal hidden" id="new-project-modal">
                    <div class="modal-content">
                        <h3>Create New Project</h3>
                        <div class="form-group">
                            <label for="project-name">Project Name:</label>
                            <input type="text" id="project-name" placeholder="my-awesome-project">
                        </div>
                        <div class="form-group">
                            <label for="project-location">Location:</label>
                            <div class="location-input">
                                <input type="text" id="project-location" placeholder="/path/to/projects">
                                <button class="browse-btn" id="browse-location-btn">Browse</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="project-template">Template:</label>
                            <select id="project-template">
                                <option value="">Select a template...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="project-description">Description:</label>
                            <textarea id="project-description" placeholder="Project description..."></textarea>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="init-git"> Initialize Git repository
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="create-readme"> Create README.md
                            </label>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="create-project-btn">Create Project</button>
                            <button class="btn-secondary" id="cancel-project-btn">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Add Dependency Modal -->
                <div class="project-modal hidden" id="add-dependency-modal">
                    <div class="modal-content">
                        <h3>Add Dependency</h3>
                        <div class="form-group">
                            <label for="dependency-name">Package Name:</label>
                            <input type="text" id="dependency-name" placeholder="package-name">
                        </div>
                        <div class="form-group">
                            <label for="dependency-version">Version:</label>
                            <input type="text" id="dependency-version" placeholder="latest">
                        </div>
                        <div class="form-group">
                            <label for="dependency-type">Type:</label>
                            <select id="dependency-type">
                                <option value="production">Production</option>
                                <option value="development">Development</option>
                            </select>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="add-dep-btn">Add Dependency</button>
                            <button class="btn-secondary" id="cancel-dep-btn">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Build Task Modal -->
                <div class="project-modal hidden" id="build-task-modal">
                    <div class="modal-content">
                        <h3>Add Build Task</h3>
                        <div class="form-group">
                            <label for="task-name">Task Name:</label>
                            <input type="text" id="task-name" placeholder="build">
                        </div>
                        <div class="form-group">
                            <label for="task-command">Command:</label>
                            <input type="text" id="task-command" placeholder="npm run build">
                        </div>
                        <div class="form-group">
                            <label for="task-description">Description:</label>
                            <textarea id="task-description" placeholder="Task description..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="task-environment">Environment:</label>
                            <select id="task-environment">
                                <option value="all">All Environments</option>
                                <option value="development">Development</option>
                                <option value="staging">Staging</option>
                                <option value="production">Production</option>
                            </select>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="add-task-btn">Add Task</button>
                            <button class="btn-secondary" id="cancel-task-btn">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Project Settings Modal -->
                <div class="project-modal hidden" id="project-settings-modal">
                    <div class="modal-content">
                        <h3>Project Settings</h3>
                        <div class="form-group">
                            <label for="settings-name">Project Name:</label>
                            <input type="text" id="settings-name">
                        </div>
                        <div class="form-group">
                            <label for="settings-description">Description:</label>
                            <textarea id="settings-description"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="settings-version">Version:</label>
                            <input type="text" id="settings-version" placeholder="1.0.0">
                        </div>
                        <div class="form-group">
                            <label for="settings-author">Author:</label>
                            <input type="text" id="settings-author">
                        </div>
                        <div class="form-group">
                            <label for="settings-license">License:</label>
                            <select id="settings-license">
                                <option value="MIT">MIT</option>
                                <option value="Apache-2.0">Apache 2.0</option>
                                <option value="GPL-3.0">GPL 3.0</option>
                                <option value="BSD-3-Clause">BSD 3-Clause</option>
                                <option value="ISC">ISC</option>
                            </select>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="save-settings-btn">Save Settings</button>
                            <button class="btn-secondary" id="cancel-settings-btn">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Header controls
        this.container.querySelector('#new-project-btn').addEventListener('click', () => {
            this.showNewProjectModal();
        });

        this.container.querySelector('#open-project-btn').addEventListener('click', () => {
            this.openProject();
        });

        this.container.querySelector('#project-settings-btn').addEventListener('click', () => {
            this.showProjectSettings();
        });

        this.container.querySelector('#project-close-btn').addEventListener('click', () => {
            this.hide();
        });

        // Environment selector
        this.container.querySelector('#environment-select').addEventListener('change', (e) => {
            this.switchEnvironment(e.target.value);
        });

        // Tab switching
        this.container.querySelectorAll('.project-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Overview actions
        this.container.querySelector('#run-project-btn').addEventListener('click', () => {
            this.runProject();
        });

        this.container.querySelector('#build-project-btn').addEventListener('click', () => {
            this.buildProject();
        });

        this.container.querySelector('#test-project-btn').addEventListener('click', () => {
            this.testProject();
        });

        this.container.querySelector('#deploy-project-btn').addEventListener('click', () => {
            this.deployProject();
        });

        // Dependency actions
        this.container.querySelector('#add-dependency-btn').addEventListener('click', () => {
            this.showAddDependencyModal();
        });

        this.container.querySelector('#update-dependencies-btn').addEventListener('click', () => {
            this.updateDependencies();
        });

        this.container.querySelector('#audit-dependencies-btn').addEventListener('click', () => {
            this.auditDependencies();
        });

        // Build actions
        this.container.querySelector('#add-build-task-btn').addEventListener('click', () => {
            this.showBuildTaskModal();
        });

        this.container.querySelector('#run-build-btn').addEventListener('click', () => {
            this.runBuild();
        });

        // Template actions
        this.container.querySelector('#create-template-btn').addEventListener('click', () => {
            this.createTemplate();
        });

        this.container.querySelector('#import-template-btn').addEventListener('click', () => {
            this.importTemplate();
        });

        // Modal event listeners
        this.setupModalEventListeners();
    }

    setupModalEventListeners() {
        // New Project Modal
        this.container.querySelector('#create-project-btn').addEventListener('click', () => {
            this.createProject();
        });

        this.container.querySelector('#cancel-project-btn').addEventListener('click', () => {
            this.hideModal('new-project-modal');
        });

        // Add Dependency Modal
        this.container.querySelector('#add-dep-btn').addEventListener('click', () => {
            this.addDependency();
        });

        this.container.querySelector('#cancel-dep-btn').addEventListener('click', () => {
            this.hideModal('add-dependency-modal');
        });

        // Build Task Modal
        this.container.querySelector('#add-task-btn').addEventListener('click', () => {
            this.addBuildTask();
        });

        this.container.querySelector('#cancel-task-btn').addEventListener('click', () => {
            this.hideModal('build-task-modal');
        });

        // Project Settings Modal
        this.container.querySelector('#save-settings-btn').addEventListener('click', () => {
            this.saveProjectSettings();
        });

        this.container.querySelector('#cancel-settings-btn').addEventListener('click', () => {
            this.hideModal('project-settings-modal');
        });

        // Browse location button
        this.container.querySelector('#browse-location-btn').addEventListener('click', () => {
            this.browseLocation();
        });
    }

    loadProjectData() {
        // Load project data from localStorage
        const savedProjects = localStorage.getItem('rustcode-projects');
        if (savedProjects) {
            this.projects = JSON.parse(savedProjects);
        }

        const currentProject = localStorage.getItem('rustcode-current-project');
        if (currentProject) {
            this.currentProject = JSON.parse(currentProject);
            this.updateProjectInfo();
        }

        this.updateRecentProjects();
    }

    saveProjectData() {
        localStorage.setItem('rustcode-projects', JSON.stringify(this.projects));
        if (this.currentProject) {
            localStorage.setItem('rustcode-current-project', JSON.stringify(this.currentProject));
        }
    }

    initializeTemplates() {
        this.templates = [
            {
                id: 'vanilla-js',
                name: 'Vanilla JavaScript',
                description: 'Basic HTML, CSS, and JavaScript project',
                icon: 'javascript',
                files: {
                    'index.html': '<!DOCTYPE html>\n<html>\n<head>\n    <title>My Project</title>\n    <link rel="stylesheet" href="style.css">\n</head>\n<body>\n    <h1>Hello World!</h1>\n    <script src="script.js"></script>\n</body>\n</html>',
                    'style.css': 'body {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n\nh1 {\n    color: #333;\n}',
                    'script.js': 'console.log("Hello World!");'
                }
            },
            {
                id: 'react-app',
                name: 'React Application',
                description: 'Modern React app with Vite',
                icon: 'react',
                dependencies: {
                    'react': '^18.0.0',
                    'react-dom': '^18.0.0'
                },
                devDependencies: {
                    'vite': '^4.0.0',
                    '@vitejs/plugin-react': '^3.0.0'
                }
            },
            {
                id: 'node-api',
                name: 'Node.js API',
                description: 'Express.js REST API server',
                icon: 'nodejs',
                dependencies: {
                    'express': '^4.18.0',
                    'cors': '^2.8.5'
                },
                devDependencies: {
                    'nodemon': '^2.0.0'
                }
            },
            {
                id: 'rust-cli',
                name: 'Rust CLI Tool',
                description: 'Command-line application in Rust',
                icon: 'rust',
                files: {
                    'Cargo.toml': '[package]\nname = "my-cli"\nversion = "0.1.0"\nedition = "2021"\n\n[dependencies]\nclap = "4.0"',
                    'src/main.rs': 'use clap::Parser;\n\n#[derive(Parser)]\n#[command(author, version, about, long_about = None)]\nstruct Args {\n    #[arg(short, long)]\n    name: Option<String>,\n}\n\nfn main() {\n    let args = Args::parse();\n    \n    match args.name {\n        Some(name) => println!("Hello {}!", name),\n        None => println!("Hello World!"),\n    }\n}'
                }
            },
            {
                id: 'python-app',
                name: 'Python Application',
                description: 'Python project with virtual environment',
                icon: 'python',
                files: {
                    'main.py': 'def main():\n    print("Hello World!")\n\nif __name__ == "__main__":\n    main()',
                    'requirements.txt': '# Add your dependencies here',
                    'README.md': '# My Python Project\n\n## Installation\n\n```bash\npip install -r requirements.txt\n```\n\n## Usage\n\n```bash\npython main.py\n```'
                }
            }
        ];

        this.updateTemplateGrid();
    }

    show() {
        this.isVisible = true;
        this.container.style.display = 'flex';
        this.updateProjectStats();
    }

    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // Project Management Methods
    showNewProjectModal() {
        const modal = this.container.querySelector('#new-project-modal');
        const templateSelect = this.container.querySelector('#project-template');

        // Populate template options
        templateSelect.innerHTML = '<option value="">Select a template...</option>';
        this.templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.id;
            option.textContent = template.name;
            templateSelect.appendChild(option);
        });

        modal.classList.remove('hidden');
    }

    createProject() {
        const name = this.container.querySelector('#project-name').value.trim();
        const location = this.container.querySelector('#project-location').value.trim();
        const templateId = this.container.querySelector('#project-template').value;
        const description = this.container.querySelector('#project-description').value.trim();
        const initGit = this.container.querySelector('#init-git').checked;
        const createReadme = this.container.querySelector('#create-readme').checked;

        if (!name) {
            alert('Please enter a project name');
            return;
        }

        const project = {
            id: Date.now().toString(),
            name: name,
            path: location ? `${location}/${name}` : name,
            description: description,
            templateId: templateId,
            createdAt: new Date().toISOString(),
            lastOpened: new Date().toISOString(),
            dependencies: new Map(),
            buildTasks: [],
            settings: {
                version: '1.0.0',
                author: '',
                license: 'MIT'
            }
        };

        // Apply template if selected
        if (templateId) {
            const template = this.templates.find(t => t.id === templateId);
            if (template) {
                if (template.dependencies) {
                    Object.entries(template.dependencies).forEach(([name, version]) => {
                        project.dependencies.set(name, { version, type: 'production' });
                    });
                }
                if (template.devDependencies) {
                    Object.entries(template.devDependencies).forEach(([name, version]) => {
                        project.dependencies.set(name, { version, type: 'development' });
                    });
                }
            }
        }

        this.projects.push(project);
        this.currentProject = project;
        this.saveProjectData();
        this.updateProjectInfo();
        this.updateRecentProjects();
        this.hideModal('new-project-modal');

        this.showNotification(`Project "${name}" created successfully!`, 'success');
    }

    openProject() {
        // In a real implementation, this would open a file dialog
        const projectPath = prompt('Enter project path:');
        if (projectPath) {
            const project = {
                id: Date.now().toString(),
                name: projectPath.split('/').pop(),
                path: projectPath,
                description: '',
                createdAt: new Date().toISOString(),
                lastOpened: new Date().toISOString(),
                dependencies: new Map(),
                buildTasks: [],
                settings: {
                    version: '1.0.0',
                    author: '',
                    license: 'MIT'
                }
            };

            this.currentProject = project;
            if (!this.projects.find(p => p.path === projectPath)) {
                this.projects.push(project);
            }

            this.saveProjectData();
            this.updateProjectInfo();
            this.updateRecentProjects();
            this.showNotification(`Project "${project.name}" opened successfully!`, 'success');
        }
    }

    updateProjectInfo() {
        if (!this.currentProject) return;

        const projectName = this.container.querySelector('.project-name');
        const projectPath = this.container.querySelector('.project-path');

        projectName.textContent = this.currentProject.name;
        projectPath.textContent = this.currentProject.path;

        this.updateProjectStats();
        this.updateDependencyLists();
        this.updateBuildTasks();
    }

    updateProjectStats() {
        // Simulate project statistics
        const fileCount = Math.floor(Math.random() * 50) + 10;
        const lineCount = Math.floor(Math.random() * 5000) + 500;
        const depCount = this.currentProject ? this.currentProject.dependencies.size : 0;

        this.container.querySelector('#file-count').textContent = fileCount;
        this.container.querySelector('#line-count').textContent = lineCount.toLocaleString();
        this.container.querySelector('#dependency-count').textContent = depCount;
    }

    updateRecentProjects() {
        const container = this.container.querySelector('#recent-projects-list');
        container.innerHTML = '';

        const recentProjects = this.projects
            .sort((a, b) => new Date(b.lastOpened) - new Date(a.lastOpened))
            .slice(0, 5);

        if (recentProjects.length === 0) {
            container.innerHTML = '<div class="empty-state">No recent projects</div>';
            return;
        }

        recentProjects.forEach(project => {
            const item = document.createElement('div');
            item.className = 'recent-project-item';

            const lastOpened = new Date(project.lastOpened);
            const timeAgo = this.getTimeAgo(lastOpened);

            item.innerHTML = `
                <div class="project-info">
                    <div class="project-name">${project.name}</div>
                    <div class="project-path">${project.path}</div>
                    <div class="project-time">${timeAgo}</div>
                </div>
                <div class="project-actions">
                    <button class="project-action-btn open-btn" title="Open">
                        ${createIcon('folder', 12)}
                    </button>
                    <button class="project-action-btn remove-btn" title="Remove">
                        ${createIcon('trash', 12)}
                    </button>
                </div>
            `;

            const openBtn = item.querySelector('.open-btn');
            const removeBtn = item.querySelector('.remove-btn');

            openBtn.addEventListener('click', () => {
                this.openRecentProject(project);
            });

            removeBtn.addEventListener('click', () => {
                this.removeRecentProject(project);
            });

            container.appendChild(item);
        });
    }

    openRecentProject(project) {
        this.currentProject = project;
        project.lastOpened = new Date().toISOString();
        this.saveProjectData();
        this.updateProjectInfo();
        this.updateRecentProjects();
        this.showNotification(`Project "${project.name}" opened!`, 'success');
    }

    removeRecentProject(project) {
        if (confirm(`Remove "${project.name}" from recent projects?`)) {
            this.projects = this.projects.filter(p => p.id !== project.id);
            this.saveProjectData();
            this.updateRecentProjects();
        }
    }

    // Environment Management
    switchEnvironment(environment) {
        this.currentEnvironment = environment;
        this.updateBuildTasks();
        this.showNotification(`Switched to ${environment} environment`, 'info');
    }

    // Dependency Management
    showAddDependencyModal() {
        this.container.querySelector('#add-dependency-modal').classList.remove('hidden');
    }

    addDependency() {
        const name = this.container.querySelector('#dependency-name').value.trim();
        const version = this.container.querySelector('#dependency-version').value.trim() || 'latest';
        const type = this.container.querySelector('#dependency-type').value;

        if (!name) {
            alert('Please enter a package name');
            return;
        }

        if (!this.currentProject) {
            alert('No project is currently open');
            return;
        }

        this.currentProject.dependencies.set(name, { version, type });
        this.saveProjectData();
        this.updateDependencyLists();
        this.updateProjectStats();
        this.hideModal('add-dependency-modal');

        this.showNotification(`Added dependency: ${name}@${version}`, 'success');
    }

    updateDependencies() {
        this.showNotification('Updating dependencies...', 'info');
        // Simulate dependency update
        setTimeout(() => {
            this.showNotification('Dependencies updated successfully!', 'success');
        }, 2000);
    }

    auditDependencies() {
        this.showNotification('Running security audit...', 'info');
        // Simulate security audit
        setTimeout(() => {
            const vulnerabilities = Math.floor(Math.random() * 3);
            if (vulnerabilities === 0) {
                this.showNotification('No security vulnerabilities found!', 'success');
            } else {
                this.showNotification(`Found ${vulnerabilities} security vulnerabilities`, 'warning');
            }
        }, 1500);
    }

    updateDependencyLists() {
        if (!this.currentProject) return;

        const prodContainer = this.container.querySelector('#prod-dependencies');
        const devContainer = this.container.querySelector('#dev-dependencies');

        prodContainer.innerHTML = '';
        devContainer.innerHTML = '';

        this.currentProject.dependencies.forEach((dep, name) => {
            const item = this.createDependencyItem(name, dep);
            if (dep.type === 'production') {
                prodContainer.appendChild(item);
            } else {
                devContainer.appendChild(item);
            }
        });

        if (prodContainer.children.length === 0) {
            prodContainer.innerHTML = '<div class="empty-state">No production dependencies</div>';
        }

        if (devContainer.children.length === 0) {
            devContainer.innerHTML = '<div class="empty-state">No development dependencies</div>';
        }
    }

    createDependencyItem(name, dep) {
        const item = document.createElement('div');
        item.className = 'dependency-item';

        item.innerHTML = `
            <div class="dependency-info">
                <div class="dependency-name">${name}</div>
                <div class="dependency-version">${dep.version}</div>
            </div>
            <div class="dependency-actions">
                <button class="dependency-action-btn update-btn" title="Update">
                    ${createIcon('refresh', 12)}
                </button>
                <button class="dependency-action-btn remove-btn" title="Remove">
                    ${createIcon('trash', 12)}
                </button>
            </div>
        `;

        const updateBtn = item.querySelector('.update-btn');
        const removeBtn = item.querySelector('.remove-btn');

        updateBtn.addEventListener('click', () => {
            this.updateDependency(name);
        });

        removeBtn.addEventListener('click', () => {
            this.removeDependency(name);
        });

        return item;
    }

    updateDependency(name) {
        this.showNotification(`Updating ${name}...`, 'info');
        setTimeout(() => {
            this.showNotification(`${name} updated successfully!`, 'success');
        }, 1000);
    }

    removeDependency(name) {
        if (confirm(`Remove dependency "${name}"?`)) {
            this.currentProject.dependencies.delete(name);
            this.saveProjectData();
            this.updateDependencyLists();
            this.updateProjectStats();
            this.showNotification(`Removed dependency: ${name}`, 'info');
        }
    }

    // Build Management
    showBuildTaskModal() {
        this.container.querySelector('#build-task-modal').classList.remove('hidden');
    }

    addBuildTask() {
        const name = this.container.querySelector('#task-name').value.trim();
        const command = this.container.querySelector('#task-command').value.trim();
        const description = this.container.querySelector('#task-description').value.trim();
        const environment = this.container.querySelector('#task-environment').value;

        if (!name || !command) {
            alert('Please enter task name and command');
            return;
        }

        if (!this.currentProject) {
            alert('No project is currently open');
            return;
        }

        const task = {
            id: Date.now().toString(),
            name,
            command,
            description,
            environment,
            createdAt: new Date().toISOString()
        };

        this.currentProject.buildTasks.push(task);
        this.saveProjectData();
        this.updateBuildTasks();
        this.hideModal('build-task-modal');

        this.showNotification(`Added build task: ${name}`, 'success');
    }

    updateBuildTasks() {
        if (!this.currentProject) return;

        const container = this.container.querySelector('#build-tasks');
        container.innerHTML = '';

        const filteredTasks = this.currentProject.buildTasks.filter(task =>
            task.environment === 'all' || task.environment === this.currentEnvironment
        );

        if (filteredTasks.length === 0) {
            container.innerHTML = '<div class="empty-state">No build tasks for this environment</div>';
            return;
        }

        filteredTasks.forEach(task => {
            const item = this.createBuildTaskItem(task);
            container.appendChild(item);
        });
    }

    createBuildTaskItem(task) {
        const item = document.createElement('div');
        item.className = 'build-task-item';

        item.innerHTML = `
            <div class="task-info">
                <div class="task-name">${task.name}</div>
                <div class="task-command">${task.command}</div>
                <div class="task-description">${task.description}</div>
            </div>
            <div class="task-actions">
                <button class="task-action-btn run-btn" title="Run Task">
                    ${createIcon('play', 12)}
                </button>
                <button class="task-action-btn edit-btn" title="Edit Task">
                    ${createIcon('edit', 12)}
                </button>
                <button class="task-action-btn remove-btn" title="Remove Task">
                    ${createIcon('trash', 12)}
                </button>
            </div>
        `;

        const runBtn = item.querySelector('.run-btn');
        const editBtn = item.querySelector('.edit-btn');
        const removeBtn = item.querySelector('.remove-btn');

        runBtn.addEventListener('click', () => {
            this.runBuildTask(task);
        });

        editBtn.addEventListener('click', () => {
            this.editBuildTask(task);
        });

        removeBtn.addEventListener('click', () => {
            this.removeBuildTask(task);
        });

        return item;
    }

    runBuildTask(task) {
        this.addBuildOutput(`Running task: ${task.name}`);
        this.addBuildOutput(`Command: ${task.command}`);
        this.addBuildOutput('');

        // Simulate task execution
        setTimeout(() => {
            this.addBuildOutput('Task completed successfully!');
            this.showNotification(`Task "${task.name}" completed!`, 'success');
        }, 2000);
    }

    addBuildOutput(message) {
        const console = this.container.querySelector('#build-output-console');
        const line = document.createElement('div');
        line.className = 'console-line';
        line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        console.appendChild(line);
        console.scrollTop = console.scrollHeight;
    }

    editBuildTask(task) {
        // Populate modal with task data for editing
        this.container.querySelector('#task-name').value = task.name;
        this.container.querySelector('#task-command').value = task.command;
        this.container.querySelector('#task-description').value = task.description;
        this.container.querySelector('#task-environment').value = task.environment;

        this.showBuildTaskModal();

        // Remove the old task when saving
        this.currentProject.buildTasks = this.currentProject.buildTasks.filter(t => t.id !== task.id);
    }

    removeBuildTask(task) {
        if (confirm(`Remove build task "${task.name}"?`)) {
            this.currentProject.buildTasks = this.currentProject.buildTasks.filter(t => t.id !== task.id);
            this.saveProjectData();
            this.updateBuildTasks();
            this.showNotification(`Removed build task: ${task.name}`, 'info');
        }
    }

    runBuild() {
        this.addBuildOutput('Starting build process...');
        this.addBuildOutput('');

        // Simulate build process
        const steps = [
            'Installing dependencies...',
            'Compiling source code...',
            'Running tests...',
            'Optimizing assets...',
            'Creating build artifacts...'
        ];

        steps.forEach((step, index) => {
            setTimeout(() => {
                this.addBuildOutput(step);
                if (index === steps.length - 1) {
                    this.addBuildOutput('');
                    this.addBuildOutput('Build completed successfully!');
                    this.showNotification('Build completed!', 'success');
                }
            }, (index + 1) * 1000);
        });
    }

    // Project Actions
    runProject() {
        this.showNotification('Starting project...', 'info');
        setTimeout(() => {
            this.showNotification('Project is running!', 'success');
        }, 1000);
    }

    buildProject() {
        this.switchTab('build');
        this.runBuild();
    }

    testProject() {
        this.showNotification('Running tests...', 'info');
        setTimeout(() => {
            const passed = Math.floor(Math.random() * 20) + 10;
            const failed = Math.floor(Math.random() * 3);
            this.showNotification(`Tests completed: ${passed} passed, ${failed} failed`,
                failed === 0 ? 'success' : 'warning');
        }, 2000);
    }

    deployProject() {
        this.showNotification(`Deploying to ${this.currentEnvironment}...`, 'info');
        setTimeout(() => {
            this.showNotification('Deployment completed successfully!', 'success');
        }, 3000);
    }

    // Template Management
    updateTemplateGrid() {
        const container = this.container.querySelector('#template-grid');
        container.innerHTML = '';

        this.templates.forEach(template => {
            const item = this.createTemplateItem(template);
            container.appendChild(item);
        });
    }

    createTemplateItem(template) {
        const item = document.createElement('div');
        item.className = 'template-item';

        item.innerHTML = `
            <div class="template-icon">
                ${createIcon(template.icon || 'file', 24)}
            </div>
            <div class="template-info">
                <div class="template-name">${template.name}</div>
                <div class="template-description">${template.description}</div>
            </div>
            <div class="template-actions">
                <button class="template-action-btn use-btn">Use Template</button>
            </div>
        `;

        const useBtn = item.querySelector('.use-btn');
        useBtn.addEventListener('click', () => {
            this.useTemplate(template);
        });

        return item;
    }

    useTemplate(template) {
        this.showNewProjectModal();
        this.container.querySelector('#project-template').value = template.id;
    }

    createTemplate() {
        if (!this.currentProject) {
            alert('No project is currently open');
            return;
        }

        const name = prompt('Enter template name:');
        if (!name) return;

        const description = prompt('Enter template description:');

        const template = {
            id: `custom-${Date.now()}`,
            name: name,
            description: description || '',
            icon: 'file',
            custom: true,
            createdAt: new Date().toISOString()
        };

        this.templates.push(template);
        this.updateTemplateGrid();
        this.showNotification(`Template "${name}" created!`, 'success');
    }

    importTemplate() {
        // In a real implementation, this would open a file dialog
        const templateData = prompt('Enter template JSON data:');
        if (templateData) {
            try {
                const template = JSON.parse(templateData);
                template.id = `imported-${Date.now()}`;
                template.imported = true;
                this.templates.push(template);
                this.updateTemplateGrid();
                this.showNotification('Template imported successfully!', 'success');
            } catch (error) {
                alert('Invalid template data');
            }
        }
    }

    // Settings Management
    showProjectSettings() {
        if (!this.currentProject) {
            alert('No project is currently open');
            return;
        }

        const modal = this.container.querySelector('#project-settings-modal');

        // Populate with current settings
        this.container.querySelector('#settings-name').value = this.currentProject.name;
        this.container.querySelector('#settings-description').value = this.currentProject.description;
        this.container.querySelector('#settings-version').value = this.currentProject.settings.version;
        this.container.querySelector('#settings-author').value = this.currentProject.settings.author;
        this.container.querySelector('#settings-license').value = this.currentProject.settings.license;

        modal.classList.remove('hidden');
    }

    saveProjectSettings() {
        if (!this.currentProject) return;

        this.currentProject.name = this.container.querySelector('#settings-name').value;
        this.currentProject.description = this.container.querySelector('#settings-description').value;
        this.currentProject.settings.version = this.container.querySelector('#settings-version').value;
        this.currentProject.settings.author = this.container.querySelector('#settings-author').value;
        this.currentProject.settings.license = this.container.querySelector('#settings-license').value;

        this.saveProjectData();
        this.updateProjectInfo();
        this.hideModal('project-settings-modal');
        this.showNotification('Project settings saved!', 'success');
    }

    // Tab Management
    switchTab(tabName) {
        // Update tab buttons
        this.container.querySelectorAll('.project-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        this.container.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab panels
        this.container.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        this.container.querySelector(`#${tabName}-panel`).classList.add('active');

        // Load tab-specific data
        switch (tabName) {
            case 'overview':
                this.updateProjectStats();
                this.updateRecentProjects();
                break;
            case 'dependencies':
                this.updateDependencyLists();
                break;
            case 'build':
                this.updateBuildTasks();
                break;
            case 'templates':
                this.updateTemplateGrid();
                break;
        }
    }

    // Utility Methods
    hideModal(modalId) {
        this.container.querySelector(`#${modalId}`).classList.add('hidden');

        // Clear form inputs
        const modal = this.container.querySelector(`#${modalId}`);
        const inputs = modal.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });
    }

    browseLocation() {
        // In a real implementation, this would open a directory picker
        const location = prompt('Enter project location:');
        if (location) {
            this.container.querySelector('#project-location').value = location;
        }
    }

    getTimeAgo(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'just now';
        if (minutes < 60) return `${minutes} minutes ago`;
        if (hours < 24) return `${hours} hours ago`;
        return `${days} days ago`;
    }

    showNotification(message, type = 'info') {
        console.log(`[Project ${type.toUpperCase()}] ${message}`);

        // If there's a status bar available, use it
        if (window.rustCodeApp && window.rustCodeApp.statusBar) {
            window.rustCodeApp.statusBar.showNotification(message, type);
        }
    }

    // Enhanced methods for advanced project management
    loadSettings() {
        const savedSettings = localStorage.getItem('project-manager-settings');
        if (savedSettings) {
            try {
                const parsed = JSON.parse(savedSettings);
                this.projectSettings = { ...this.projectSettings, ...parsed };
            } catch (error) {
                console.warn('Failed to load project manager settings:', error);
            }
        }
    }

    saveSettings() {
        localStorage.setItem('project-manager-settings', JSON.stringify(this.projectSettings));
    }

    initializeBuildSystems() {
        // Initialize build system configurations
        this.buildSystemConfigs = {
            npm: {
                name: 'NPM',
                configFile: 'package.json',
                buildCommand: 'npm run build',
                testCommand: 'npm test',
                installCommand: 'npm install',
                startCommand: 'npm start',
                supportedLanguages: ['javascript', 'typescript']
            },
            yarn: {
                name: 'Yarn',
                configFile: 'package.json',
                buildCommand: 'yarn build',
                testCommand: 'yarn test',
                installCommand: 'yarn install',
                startCommand: 'yarn start',
                supportedLanguages: ['javascript', 'typescript']
            },
            cargo: {
                name: 'Cargo',
                configFile: 'Cargo.toml',
                buildCommand: 'cargo build',
                testCommand: 'cargo test',
                installCommand: 'cargo fetch',
                startCommand: 'cargo run',
                supportedLanguages: ['rust']
            },
            maven: {
                name: 'Maven',
                configFile: 'pom.xml',
                buildCommand: 'mvn compile',
                testCommand: 'mvn test',
                installCommand: 'mvn install',
                startCommand: 'mvn exec:java',
                supportedLanguages: ['java']
            },
            gradle: {
                name: 'Gradle',
                configFile: 'build.gradle',
                buildCommand: 'gradle build',
                testCommand: 'gradle test',
                installCommand: 'gradle dependencies',
                startCommand: 'gradle run',
                supportedLanguages: ['java', 'kotlin', 'groovy']
            }
        };
    }

    setupProjectWatchers() {
        if (!this.projectSettings.enableAdvancedFeatures) return;

        // Setup file watchers for project changes
        this.watchers.set('config', {
            patterns: ['package.json', 'Cargo.toml', 'pom.xml', 'build.gradle'],
            callback: this.onConfigFileChange.bind(this)
        });

        this.watchers.set('source', {
            patterns: ['src/**/*', 'lib/**/*', 'app/**/*'],
            callback: this.onSourceFileChange.bind(this)
        });

        this.watchers.set('test', {
            patterns: ['test/**/*', 'tests/**/*', '__tests__/**/*'],
            callback: this.onTestFileChange.bind(this)
        });
    }

    onConfigFileChange(filePath) {
        console.log(`Config file changed: ${filePath}`);
        this.analyzeProjectStructure();
        this.updateDependencyLists();
        this.showNotification('Project configuration updated', 'info');
    }

    onSourceFileChange(filePath) {
        console.log(`Source file changed: ${filePath}`);
        this.updateProjectMetrics();

        if (this.projectSettings.autoFormat) {
            this.formatFile(filePath);
        }

        if (this.projectSettings.enableLinting) {
            this.lintFile(filePath);
        }
    }

    onTestFileChange(filePath) {
        console.log(`Test file changed: ${filePath}`);
        this.updateProjectMetrics();

        if (this.projectSettings.enableTesting) {
            this.runTestsForFile(filePath);
        }
    }

    startPerformanceMonitoring() {
        if (!this.projectSettings.enableAdvancedFeatures) return;

        // Monitor project performance metrics
        setInterval(() => {
            this.updatePerformanceMetrics();
        }, 30000); // Update every 30 seconds
    }

    updatePerformanceMetrics() {
        // Update performance metrics
        this.performanceMetrics.lastAnalysis = new Date();

        // Analyze build times
        if (this.performanceMetrics.buildTimes.length > 10) {
            this.performanceMetrics.buildTimes = this.performanceMetrics.buildTimes.slice(-5);
        }

        // Calculate averages
        const avgBuildTime = this.performanceMetrics.buildTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.buildTimes.length || 0;
        const avgTestTime = this.performanceMetrics.testTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.testTimes.length || 0;

        // Update UI if performance tab is visible
        this.updatePerformanceDisplay(avgBuildTime, avgTestTime);
    }

    updatePerformanceDisplay(avgBuildTime, avgTestTime) {
        const performanceContainer = this.container.querySelector('#performance-metrics');
        if (performanceContainer) {
            performanceContainer.innerHTML = `
                <div class="metric-item">
                    <span class="metric-label">Avg Build Time:</span>
                    <span class="metric-value">${avgBuildTime.toFixed(2)}ms</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Avg Test Time:</span>
                    <span class="metric-value">${avgTestTime.toFixed(2)}ms</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Last Analysis:</span>
                    <span class="metric-value">${this.getTimeAgo(this.performanceMetrics.lastAnalysis)}</span>
                </div>
            `;
        }
    }

    // Advanced project operations
    async analyzeProjectStructure() {
        // Analyze project structure and detect patterns
        const structure = {
            type: this.detectProjectType(),
            buildSystem: this.detectBuildSystem(),
            languages: this.detectLanguages(),
            frameworks: this.detectFrameworks(),
            testFrameworks: this.detectTestFrameworks()
        };

        this.currentProject.structure = structure;
        this.saveProjectData();

        return structure;
    }

    detectProjectType() {
        // Detect project type based on files and structure
        if (this.hasFile('package.json')) {
            const packageJson = this.getFileContent('package.json');
            if (packageJson.includes('react') || packageJson.includes('vue') || packageJson.includes('angular')) {
                return 'web';
            }
            if (packageJson.includes('electron')) {
                return 'desktop';
            }
            if (packageJson.includes('express') || packageJson.includes('fastify')) {
                return 'api';
            }
        }

        if (this.hasFile('Cargo.toml')) {
            const cargoToml = this.getFileContent('Cargo.toml');
            if (cargoToml.includes('[[bin]]')) {
                return 'cli';
            }
            if (cargoToml.includes('[lib]')) {
                return 'library';
            }
        }

        return 'unknown';
    }

    detectBuildSystem() {
        if (this.hasFile('package.json')) return 'npm';
        if (this.hasFile('Cargo.toml')) return 'cargo';
        if (this.hasFile('pom.xml')) return 'maven';
        if (this.hasFile('build.gradle')) return 'gradle';
        if (this.hasFile('Makefile')) return 'make';
        if (this.hasFile('CMakeLists.txt')) return 'cmake';
        return 'unknown';
    }

    detectLanguages() {
        const languages = [];

        if (this.hasFiles(['*.js', '*.jsx'])) languages.push('javascript');
        if (this.hasFiles(['*.ts', '*.tsx'])) languages.push('typescript');
        if (this.hasFiles(['*.rs'])) languages.push('rust');
        if (this.hasFiles(['*.py'])) languages.push('python');
        if (this.hasFiles(['*.java'])) languages.push('java');
        if (this.hasFiles(['*.go'])) languages.push('go');
        if (this.hasFiles(['*.cpp', '*.c'])) languages.push('c++');

        return languages;
    }

    detectFrameworks() {
        const frameworks = [];

        if (this.hasFile('package.json')) {
            const packageJson = this.getFileContent('package.json');
            if (packageJson.includes('react')) frameworks.push('react');
            if (packageJson.includes('vue')) frameworks.push('vue');
            if (packageJson.includes('angular')) frameworks.push('angular');
            if (packageJson.includes('express')) frameworks.push('express');
            if (packageJson.includes('next')) frameworks.push('nextjs');
        }

        return frameworks;
    }

    detectTestFrameworks() {
        const testFrameworks = [];

        if (this.hasFile('package.json')) {
            const packageJson = this.getFileContent('package.json');
            if (packageJson.includes('jest')) testFrameworks.push('jest');
            if (packageJson.includes('mocha')) testFrameworks.push('mocha');
            if (packageJson.includes('vitest')) testFrameworks.push('vitest');
            if (packageJson.includes('cypress')) testFrameworks.push('cypress');
        }

        if (this.hasFile('Cargo.toml')) {
            testFrameworks.push('cargo-test');
        }

        return testFrameworks;
    }

    // Helper methods for file detection
    hasFile(filename) {
        // Simulate file existence check
        return Math.random() > 0.5; // Random for demo
    }

    hasFiles(patterns) {
        // Simulate file pattern matching
        return patterns.some(pattern => Math.random() > 0.7); // Random for demo
    }

    getFileContent(filename) {
        // Simulate file content retrieval
        if (filename === 'package.json') {
            return JSON.stringify({
                name: 'demo-project',
                version: '1.0.0',
                dependencies: {
                    react: '^18.0.0',
                    express: '^4.18.0'
                },
                devDependencies: {
                    jest: '^29.0.0',
                    '@types/node': '^18.0.0'
                }
            }, null, 2);
        }
        return '';
    }

    // Advanced build and deployment operations
    async formatFile(filePath) {
        // Simulate file formatting
        console.log(`Formatting file: ${filePath}`);
        // In a real implementation, this would use language-specific formatters
    }

    async lintFile(filePath) {
        // Simulate file linting
        console.log(`Linting file: ${filePath}`);
        // In a real implementation, this would use language-specific linters
    }

    async runTestsForFile(filePath) {
        // Simulate running tests for a specific file
        console.log(`Running tests for: ${filePath}`);
        const startTime = performance.now();

        // Simulate test execution
        await new Promise(resolve => setTimeout(resolve, 1000));

        const duration = performance.now() - startTime;
        this.performanceMetrics.testTimes.push(duration);

        this.showNotification(`Tests completed for ${filePath} (${duration.toFixed(2)}ms)`, 'success');
    }

    // Enhanced cleanup and disposal
    dispose() {
        // Clear all watchers
        this.watchers.clear();

        // Clear all active processes
        this.activeProcesses.forEach(process => {
            if (process && typeof process.kill === 'function') {
                process.kill();
            }
        });
        this.activeProcesses.clear();

        // Clear event listeners
        this.eventListeners.forEach(listener => {
            if (listener.element && listener.event && listener.handler) {
                listener.element.removeEventListener(listener.event, listener.handler);
            }
        });
        this.eventListeners.length = 0;

        // Save state before disposal
        this.saveProjectData();
        this.saveSettings();

        console.log('Project Manager disposed successfully');
    }
}
