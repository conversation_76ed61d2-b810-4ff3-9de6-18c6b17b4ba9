import { createIcon } from '../icons/icon-library.js';

export class GitIntegration {
    constructor(container, fileExplorer) {
        this.container = container;
        this.fileExplorer = fileExplorer;
        this.isVisible = false;
        this.currentBranch = 'main';
        this.branches = ['main'];
        this.commits = [];
        this.stagedFiles = new Set();
        this.modifiedFiles = new Set();
        this.untrackedFiles = new Set();
        this.conflictFiles = new Set();
        this.remotes = [];
        this.gitStatus = 'clean';
        this.gitConfig = {
            userName: '',
            userEmail: '',
            defaultBranch: 'main',
            autoFetch: false,
            autoStage: false,
            enableAdvancedFeatures: true,
            enableGitHooks: true,
            enableSubmodules: false
        };
        this.gitHistory = [];
        this.stashList = [];
        this.tags = [];
        this.workingDirectory = '';
        this.repositoryInfo = {
            isRepository: false,
            hasRemote: false,
            ahead: 0,
            behind: 0,
            lastCommit: null,
            totalCommits: 0
        };
        this.performanceMetrics = {
            lastRefresh: null,
            refreshDuration: 0,
            operationHistory: []
        };
        this.settings = {
            enableRealTimeSync: false,
            showFileHistory: true,
            enableBlameAnnotations: true,
            enableDiffHighlighting: true,
            maxHistoryItems: 100,
            autoRefreshInterval: 30000
        };
        this.eventListeners = [];
        this.refreshTimer = null;

        this.init();
    }

    init() {
        this.loadSettings();
        this.createUI();
        this.setupEventListeners();
        this.loadGitState();
        this.initializeRepository();
        this.startAutoRefresh();
        this.loadGitConfig();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="git-panel">
                <div class="git-header">
                    <div class="git-title">
                        ${createIcon('gitBranch', 16)} Source Control
                    </div>
                    <div class="git-controls">
                        <button class="git-btn" id="git-refresh-btn" title="Refresh">
                            ${createIcon('refresh', 14)}
                        </button>
                        <button class="git-btn" id="git-settings-btn" title="Git Settings">
                            ${createIcon('settings', 14)}
                        </button>
                        <button class="git-btn" id="git-close-btn" title="Close">
                            ${createIcon('close', 14)}
                        </button>
                    </div>
                </div>

                <div class="git-branch-info">
                    <div class="current-branch">
                        <span class="branch-icon">${createIcon('gitBranch', 14)}</span>
                        <span class="branch-name" id="current-branch-name">${this.currentBranch}</span>
                        <button class="branch-dropdown-btn" id="branch-dropdown-btn">
                            ${createIcon('chevronDown', 12)}
                        </button>
                    </div>
                    <div class="git-status" id="git-status">
                        <span class="status-text">Working tree clean</span>
                    </div>
                </div>

                <div class="git-actions">
                    <button class="git-action-btn primary" id="commit-btn" disabled>
                        ${createIcon('check', 14)} Commit
                    </button>
                    <button class="git-action-btn" id="pull-btn">
                        ${createIcon('chevronDown', 14)} Pull
                    </button>
                    <button class="git-action-btn" id="push-btn">
                        ${createIcon('chevronUp', 14)} Push
                    </button>
                    <button class="git-action-btn" id="sync-btn">
                        ${createIcon('refresh', 14)} Sync
                    </button>
                </div>

                <div class="git-sections">
                    <!-- Staged Changes -->
                    <div class="git-section" id="staged-section">
                        <div class="section-header">
                            <button class="section-toggle" data-section="staged">
                                ${createIcon('chevronDown', 12)}
                            </button>
                            <span class="section-title">Staged Changes</span>
                            <span class="section-count" id="staged-count">0</span>
                            <div class="section-actions">
                                <button class="section-action-btn" id="unstage-all-btn" title="Unstage All">
                                    ${createIcon('minus', 12)}
                                </button>
                            </div>
                        </div>
                        <div class="section-content" id="staged-files"></div>
                    </div>

                    <!-- Changes -->
                    <div class="git-section" id="changes-section">
                        <div class="section-header">
                            <button class="section-toggle" data-section="changes">
                                ${createIcon('chevronDown', 12)}
                            </button>
                            <span class="section-title">Changes</span>
                            <span class="section-count" id="changes-count">0</span>
                            <div class="section-actions">
                                <button class="section-action-btn" id="stage-all-btn" title="Stage All">
                                    ${createIcon('plus', 12)}
                                </button>
                                <button class="section-action-btn" id="discard-all-btn" title="Discard All">
                                    ${createIcon('trash', 12)}
                                </button>
                            </div>
                        </div>
                        <div class="section-content" id="changed-files"></div>
                    </div>

                    <!-- Untracked Files -->
                    <div class="git-section" id="untracked-section">
                        <div class="section-header">
                            <button class="section-toggle" data-section="untracked">
                                ${createIcon('chevronDown', 12)}
                            </button>
                            <span class="section-title">Untracked Files</span>
                            <span class="section-count" id="untracked-count">0</span>
                            <div class="section-actions">
                                <button class="section-action-btn" id="add-all-btn" title="Add All">
                                    ${createIcon('plus', 12)}
                                </button>
                            </div>
                        </div>
                        <div class="section-content" id="untracked-files"></div>
                    </div>
                </div>

                <div class="git-tabs">
                    <button class="git-tab active" data-tab="changes">Changes</button>
                    <button class="git-tab" data-tab="history">History</button>
                    <button class="git-tab" data-tab="branches">Branches</button>
                    <button class="git-tab" data-tab="remotes">Remotes</button>
                </div>

                <div class="git-tab-content">
                    <div class="tab-panel active" id="changes-panel">
                        <!-- Changes content already above -->
                    </div>

                    <div class="tab-panel" id="history-panel">
                        <div class="commit-history" id="commit-history">
                            <!-- Commit history will be populated here -->
                        </div>
                    </div>

                    <div class="tab-panel" id="branches-panel">
                        <div class="branch-actions">
                            <button class="branch-action-btn" id="new-branch-btn">
                                ${createIcon('plus', 14)} New Branch
                            </button>
                            <button class="branch-action-btn" id="merge-branch-btn">
                                ${createIcon('gitBranch', 14)} Merge
                            </button>
                        </div>
                        <div class="branch-list" id="branch-list">
                            <!-- Branch list will be populated here -->
                        </div>
                    </div>

                    <div class="tab-panel" id="remotes-panel">
                        <div class="remote-actions">
                            <button class="remote-action-btn" id="add-remote-btn">
                                ${createIcon('plus', 14)} Add Remote
                            </button>
                            <button class="remote-action-btn" id="fetch-btn">
                                ${createIcon('chevronDown', 14)} Fetch
                            </button>
                        </div>
                        <div class="remote-list" id="remote-list">
                            <!-- Remote list will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Commit Message Input -->
                <div class="commit-input-section" id="commit-input-section">
                    <textarea
                        id="commit-message"
                        placeholder="Enter commit message..."
                        rows="3"
                    ></textarea>
                    <div class="commit-actions">
                        <button class="commit-action-btn" id="commit-and-push-btn">
                            Commit & Push
                        </button>
                        <button class="commit-action-btn secondary" id="commit-only-btn">
                            Commit
                        </button>
                    </div>
                </div>

                <!-- Branch Dropdown -->
                <div class="branch-dropdown hidden" id="branch-dropdown">
                    <div class="dropdown-header">
                        <input type="text" id="branch-search" placeholder="Search branches...">
                    </div>
                    <div class="dropdown-content" id="branch-dropdown-content">
                        <!-- Branch options will be populated here -->
                    </div>
                </div>

                <!-- Diff Viewer -->
                <div class="diff-viewer hidden" id="diff-viewer">
                    <div class="diff-header">
                        <span class="diff-file-name" id="diff-file-name"></span>
                        <button class="diff-close-btn" id="diff-close-btn">
                            ${createIcon('close', 14)}
                        </button>
                    </div>
                    <div class="diff-content" id="diff-content">
                        <!-- Diff content will be populated here -->
                    </div>
                </div>

                <!-- Git Settings Modal -->
                <div class="git-settings-modal hidden" id="git-settings-modal">
                    <div class="settings-content">
                        <h3>Git Settings</h3>
                        <div class="setting-group">
                            <label for="git-user-name">User Name:</label>
                            <input type="text" id="git-user-name" placeholder="Your Name">
                        </div>
                        <div class="setting-group">
                            <label for="git-user-email">User Email:</label>
                            <input type="email" id="git-user-email" placeholder="<EMAIL>">
                        </div>
                        <div class="setting-group">
                            <label for="git-default-branch">Default Branch:</label>
                            <input type="text" id="git-default-branch" value="main">
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="git-auto-fetch">
                                Auto-fetch from remote
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="git-auto-stage">
                                Auto-stage modified files
                            </label>
                        </div>
                        <div class="settings-actions">
                            <button class="btn-primary" id="save-git-settings">Save</button>
                            <button class="btn-secondary" id="cancel-git-settings">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Control buttons
        this.container.querySelector('#git-refresh-btn').addEventListener('click', () => {
            this.refreshGitStatus();
        });

        this.container.querySelector('#git-settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        this.container.querySelector('#git-close-btn').addEventListener('click', () => {
            this.hide();
        });

        // Branch dropdown
        this.container.querySelector('#branch-dropdown-btn').addEventListener('click', () => {
            this.toggleBranchDropdown();
        });

        // Git actions
        this.container.querySelector('#commit-btn').addEventListener('click', () => {
            this.showCommitInput();
        });

        this.container.querySelector('#pull-btn').addEventListener('click', () => {
            this.pullChanges();
        });

        this.container.querySelector('#push-btn').addEventListener('click', () => {
            this.pushChanges();
        });

        this.container.querySelector('#sync-btn').addEventListener('click', () => {
            this.syncChanges();
        });

        // Section toggles
        this.container.querySelectorAll('.section-toggle').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.toggleSection(e.target.dataset.section);
            });
        });

        // Section actions
        this.container.querySelector('#stage-all-btn').addEventListener('click', () => {
            this.stageAllFiles();
        });

        this.container.querySelector('#unstage-all-btn').addEventListener('click', () => {
            this.unstageAllFiles();
        });

        this.container.querySelector('#discard-all-btn').addEventListener('click', () => {
            this.discardAllChanges();
        });

        this.container.querySelector('#add-all-btn').addEventListener('click', () => {
            this.addAllFiles();
        });

        // Tab switching
        this.container.querySelectorAll('.git-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Branch actions
        this.container.querySelector('#new-branch-btn').addEventListener('click', () => {
            this.createNewBranch();
        });

        this.container.querySelector('#merge-branch-btn').addEventListener('click', () => {
            this.mergeBranch();
        });

        // Remote actions
        this.container.querySelector('#add-remote-btn').addEventListener('click', () => {
            this.addRemote();
        });

        this.container.querySelector('#fetch-btn').addEventListener('click', () => {
            this.fetchFromRemote();
        });

        // Commit actions
        this.container.querySelector('#commit-and-push-btn').addEventListener('click', () => {
            this.commitAndPush();
        });

        this.container.querySelector('#commit-only-btn').addEventListener('click', () => {
            this.commitOnly();
        });

        // Diff viewer
        this.container.querySelector('#diff-close-btn').addEventListener('click', () => {
            this.closeDiffViewer();
        });

        // Settings modal
        this.container.querySelector('#save-git-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        this.container.querySelector('#cancel-git-settings').addEventListener('click', () => {
            this.hideSettings();
        });

        // Commit message input
        const commitMessage = this.container.querySelector('#commit-message');
        commitMessage.addEventListener('input', () => {
            this.updateCommitButton();
        });

        commitMessage.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.commitOnly();
            }
        });
    }

    loadGitState() {
        // Load git state from localStorage or initialize
        const savedState = localStorage.getItem('rustcode-git-state');
        if (savedState) {
            const state = JSON.parse(savedState);
            this.currentBranch = state.currentBranch || 'main';
            this.branches = state.branches || ['main'];
            this.commits = state.commits || [];
            this.remotes = state.remotes || [];
        }

        this.updateUI();
    }

    saveGitState() {
        const state = {
            currentBranch: this.currentBranch,
            branches: this.branches,
            commits: this.commits,
            remotes: this.remotes
        };
        localStorage.setItem('rustcode-git-state', JSON.stringify(state));
    }

    show() {
        this.isVisible = true;
        this.container.style.display = 'flex';
        this.refreshGitStatus();
    }

    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    refreshGitStatus() {
        // Simulate git status check
        this.simulateGitStatus();
        this.updateUI();
    }

    simulateGitStatus() {
        // For demo purposes, simulate some git changes
        // In a real implementation, this would call actual git commands

        // Simulate some modified files
        this.modifiedFiles.clear();
        this.untrackedFiles.clear();
        this.stagedFiles.clear();

        // Add some demo files
        if (Math.random() > 0.5) {
            this.modifiedFiles.add('src/main.js');
            this.modifiedFiles.add('src/components/editor.js');
        }

        if (Math.random() > 0.7) {
            this.untrackedFiles.add('new-feature.js');
            this.untrackedFiles.add('temp-file.txt');
        }

        this.updateGitStatus();
    }

    updateGitStatus() {
        const totalChanges = this.modifiedFiles.size + this.untrackedFiles.size + this.stagedFiles.size;

        if (totalChanges === 0) {
            this.gitStatus = 'clean';
        } else if (this.stagedFiles.size > 0) {
            this.gitStatus = 'staged';
        } else {
            this.gitStatus = 'modified';
        }
    }

    updateUI() {
        this.updateBranchInfo();
        this.updateFileLists();
        this.updateCounts();
        this.updateCommitButton();
        this.updateBranchList();
        this.updateCommitHistory();
        this.updateRemoteList();
    }

    updateBranchInfo() {
        const branchName = this.container.querySelector('#current-branch-name');
        const gitStatus = this.container.querySelector('#git-status .status-text');

        branchName.textContent = this.currentBranch;

        switch (this.gitStatus) {
            case 'clean':
                gitStatus.textContent = 'Working tree clean';
                gitStatus.className = 'status-text clean';
                break;
            case 'modified':
                gitStatus.textContent = `${this.modifiedFiles.size + this.untrackedFiles.size} changes`;
                gitStatus.className = 'status-text modified';
                break;
            case 'staged':
                gitStatus.textContent = `${this.stagedFiles.size} staged, ${this.modifiedFiles.size + this.untrackedFiles.size} changes`;
                gitStatus.className = 'status-text staged';
                break;
        }
    }

    updateFileLists() {
        this.updateStagedFiles();
        this.updateChangedFiles();
        this.updateUntrackedFiles();
    }

    updateStagedFiles() {
        const container = this.container.querySelector('#staged-files');
        container.innerHTML = '';

        this.stagedFiles.forEach(file => {
            const fileItem = this.createFileItem(file, 'staged');
            container.appendChild(fileItem);
        });
    }

    updateChangedFiles() {
        const container = this.container.querySelector('#changed-files');
        container.innerHTML = '';

        this.modifiedFiles.forEach(file => {
            const fileItem = this.createFileItem(file, 'modified');
            container.appendChild(fileItem);
        });
    }

    updateUntrackedFiles() {
        const container = this.container.querySelector('#untracked-files');
        container.innerHTML = '';

        this.untrackedFiles.forEach(file => {
            const fileItem = this.createFileItem(file, 'untracked');
            container.appendChild(fileItem);
        });
    }

    createFileItem(filePath, status) {
        const item = document.createElement('div');
        item.className = 'git-file-item';
        item.dataset.file = filePath;
        item.dataset.status = status;

        const statusIcon = this.getStatusIcon(status);
        const fileName = filePath.split('/').pop();
        const filePath_display = filePath;

        item.innerHTML = `
            <div class="file-info">
                <span class="file-status-icon">${statusIcon}</span>
                <span class="file-name" title="${filePath_display}">${fileName}</span>
                <span class="file-path">${filePath_display}</span>
            </div>
            <div class="file-actions">
                ${this.getFileActions(status)}
            </div>
        `;

        // Add event listeners
        item.querySelector('.file-info').addEventListener('click', () => {
            this.showFileDiff(filePath);
        });

        this.setupFileActions(item, filePath, status);

        return item;
    }

    getStatusIcon(status) {
        switch (status) {
            case 'staged':
                return createIcon('check', 14);
            case 'modified':
                return createIcon('edit', 14);
            case 'untracked':
                return createIcon('plus', 14);
            case 'conflict':
                return createIcon('warning', 14);
            default:
                return createIcon('file', 14);
        }
    }

    getFileActions(status) {
        switch (status) {
            case 'staged':
                return `
                    <button class="file-action-btn unstage-btn" title="Unstage">
                        ${createIcon('minus', 12)}
                    </button>
                `;
            case 'modified':
                return `
                    <button class="file-action-btn stage-btn" title="Stage">
                        ${createIcon('plus', 12)}
                    </button>
                    <button class="file-action-btn discard-btn" title="Discard">
                        ${createIcon('trash', 12)}
                    </button>
                `;
            case 'untracked':
                return `
                    <button class="file-action-btn add-btn" title="Add">
                        ${createIcon('plus', 12)}
                    </button>
                    <button class="file-action-btn ignore-btn" title="Ignore">
                        ${createIcon('close', 12)}
                    </button>
                `;
            default:
                return '';
        }
    }

    setupFileActions(item, filePath, status) {
        const stageBtn = item.querySelector('.stage-btn');
        const unstageBtn = item.querySelector('.unstage-btn');
        const discardBtn = item.querySelector('.discard-btn');
        const addBtn = item.querySelector('.add-btn');
        const ignoreBtn = item.querySelector('.ignore-btn');

        if (stageBtn) {
            stageBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.stageFile(filePath);
            });
        }

        if (unstageBtn) {
            unstageBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.unstageFile(filePath);
            });
        }

        if (discardBtn) {
            discardBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.discardFile(filePath);
            });
        }

        if (addBtn) {
            addBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.addFile(filePath);
            });
        }

        if (ignoreBtn) {
            ignoreBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.ignoreFile(filePath);
            });
        }
    }

    updateCounts() {
        this.container.querySelector('#staged-count').textContent = this.stagedFiles.size;
        this.container.querySelector('#changes-count').textContent = this.modifiedFiles.size;
        this.container.querySelector('#untracked-count').textContent = this.untrackedFiles.size;
    }

    updateCommitButton() {
        const commitBtn = this.container.querySelector('#commit-btn');
        const commitMessage = this.container.querySelector('#commit-message');

        const hasStaged = this.stagedFiles.size > 0;
        const hasMessage = commitMessage && commitMessage.value.trim().length > 0;

        commitBtn.disabled = !hasStaged;

        if (hasStaged) {
            commitBtn.textContent = `Commit (${this.stagedFiles.size})`;
        } else {
            commitBtn.textContent = 'Commit';
        }
    }

    // File operations
    stageFile(filePath) {
        this.modifiedFiles.delete(filePath);
        this.untrackedFiles.delete(filePath);
        this.stagedFiles.add(filePath);
        this.updateGitStatus();
        this.updateUI();
    }

    unstageFile(filePath) {
        this.stagedFiles.delete(filePath);
        this.modifiedFiles.add(filePath);
        this.updateGitStatus();
        this.updateUI();
    }

    discardFile(filePath) {
        this.modifiedFiles.delete(filePath);
        this.updateGitStatus();
        this.updateUI();
    }

    addFile(filePath) {
        this.untrackedFiles.delete(filePath);
        this.stagedFiles.add(filePath);
        this.updateGitStatus();
        this.updateUI();
    }

    ignoreFile(filePath) {
        this.untrackedFiles.delete(filePath);
        this.updateGitStatus();
        this.updateUI();
    }

    stageAllFiles() {
        this.modifiedFiles.forEach(file => this.stagedFiles.add(file));
        this.modifiedFiles.clear();
        this.updateGitStatus();
        this.updateUI();
    }

    unstageAllFiles() {
        this.stagedFiles.forEach(file => this.modifiedFiles.add(file));
        this.stagedFiles.clear();
        this.updateGitStatus();
        this.updateUI();
    }

    discardAllChanges() {
        if (confirm('Are you sure you want to discard all changes? This cannot be undone.')) {
            this.modifiedFiles.clear();
            this.updateGitStatus();
            this.updateUI();
        }
    }

    addAllFiles() {
        this.untrackedFiles.forEach(file => this.stagedFiles.add(file));
        this.untrackedFiles.clear();
        this.updateGitStatus();
        this.updateUI();
    }

    // Commit operations
    showCommitInput() {
        const commitSection = this.container.querySelector('#commit-input-section');
        commitSection.style.display = 'block';
        this.container.querySelector('#commit-message').focus();
    }

    commitOnly() {
        const message = this.container.querySelector('#commit-message').value.trim();
        if (!message) {
            alert('Please enter a commit message');
            return;
        }

        if (this.stagedFiles.size === 0) {
            alert('No files staged for commit');
            return;
        }

        this.performCommit(message);
    }

    commitAndPush() {
        const message = this.container.querySelector('#commit-message').value.trim();
        if (!message) {
            alert('Please enter a commit message');
            return;
        }

        if (this.stagedFiles.size === 0) {
            alert('No files staged for commit');
            return;
        }

        this.performCommit(message);
        this.pushChanges();
    }

    performCommit(message) {
        const commit = {
            id: this.generateCommitId(),
            message: message,
            author: this.getGitUser(),
            date: new Date().toISOString(),
            files: Array.from(this.stagedFiles),
            branch: this.currentBranch
        };

        this.commits.unshift(commit);
        this.stagedFiles.clear();
        this.container.querySelector('#commit-message').value = '';
        this.container.querySelector('#commit-input-section').style.display = 'none';

        this.updateGitStatus();
        this.updateUI();
        this.saveGitState();

        this.showNotification(`Committed: ${message}`, 'success');
    }

    generateCommitId() {
        return Math.random().toString(36).substr(2, 8);
    }

    getGitUser() {
        const userName = localStorage.getItem('git-user-name') || 'User';
        const userEmail = localStorage.getItem('git-user-email') || '<EMAIL>';
        return `${userName} <${userEmail}>`;
    }

    // Branch operations
    toggleBranchDropdown() {
        const dropdown = this.container.querySelector('#branch-dropdown');
        dropdown.classList.toggle('hidden');

        if (!dropdown.classList.contains('hidden')) {
            this.updateBranchDropdown();
        }
    }

    updateBranchDropdown() {
        const content = this.container.querySelector('#branch-dropdown-content');
        content.innerHTML = '';

        this.branches.forEach(branch => {
            const item = document.createElement('div');
            item.className = 'branch-dropdown-item';
            if (branch === this.currentBranch) {
                item.classList.add('current');
            }

            item.innerHTML = `
                <span class="branch-icon">${createIcon('gitBranch', 14)}</span>
                <span class="branch-name">${branch}</span>
                ${branch === this.currentBranch ? createIcon('check', 14) : ''}
            `;

            item.addEventListener('click', () => {
                this.switchBranch(branch);
                this.toggleBranchDropdown();
            });

            content.appendChild(item);
        });
    }

    switchBranch(branchName) {
        if (branchName !== this.currentBranch) {
            this.currentBranch = branchName;
            this.updateUI();
            this.saveGitState();
            this.showNotification(`Switched to branch: ${branchName}`, 'info');
        }
    }

    createNewBranch() {
        const branchName = prompt('Enter new branch name:');
        if (branchName && !this.branches.includes(branchName)) {
            this.branches.push(branchName);
            this.currentBranch = branchName;
            this.updateUI();
            this.saveGitState();
            this.showNotification(`Created and switched to branch: ${branchName}`, 'success');
        }
    }

    mergeBranch() {
        const branchToMerge = prompt('Enter branch name to merge:');
        if (branchToMerge && this.branches.includes(branchToMerge) && branchToMerge !== this.currentBranch) {
            // Simulate merge
            this.showNotification(`Merged ${branchToMerge} into ${this.currentBranch}`, 'success');
        }
    }

    updateBranchList() {
        const container = this.container.querySelector('#branch-list');
        container.innerHTML = '';

        this.branches.forEach(branch => {
            const item = document.createElement('div');
            item.className = 'branch-list-item';
            if (branch === this.currentBranch) {
                item.classList.add('current');
            }

            item.innerHTML = `
                <div class="branch-info">
                    <span class="branch-icon">${createIcon('gitBranch', 14)}</span>
                    <span class="branch-name">${branch}</span>
                    ${branch === this.currentBranch ? '<span class="current-indicator">current</span>' : ''}
                </div>
                <div class="branch-actions">
                    ${branch !== this.currentBranch ? `
                        <button class="branch-action-btn checkout-btn" title="Checkout">
                            ${createIcon('check', 12)}
                        </button>
                        <button class="branch-action-btn delete-btn" title="Delete">
                            ${createIcon('trash', 12)}
                        </button>
                    ` : ''}
                </div>
            `;

            // Add event listeners
            const checkoutBtn = item.querySelector('.checkout-btn');
            const deleteBtn = item.querySelector('.delete-btn');

            if (checkoutBtn) {
                checkoutBtn.addEventListener('click', () => {
                    this.switchBranch(branch);
                });
            }

            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => {
                    this.deleteBranch(branch);
                });
            }

            container.appendChild(item);
        });
    }

    deleteBranch(branchName) {
        if (confirm(`Are you sure you want to delete branch "${branchName}"?`)) {
            this.branches = this.branches.filter(b => b !== branchName);
            this.updateUI();
            this.saveGitState();
            this.showNotification(`Deleted branch: ${branchName}`, 'info');
        }
    }

    // Remote operations
    addRemote() {
        const remoteName = prompt('Enter remote name (e.g., origin):');
        if (!remoteName) return;

        const remoteUrl = prompt('Enter remote URL:');
        if (!remoteUrl) return;

        const remote = {
            name: remoteName,
            url: remoteUrl,
            type: 'fetch'
        };

        this.remotes.push(remote);
        this.updateUI();
        this.saveGitState();
        this.showNotification(`Added remote: ${remoteName}`, 'success');
    }

    fetchFromRemote() {
        this.showNotification('Fetching from remote...', 'info');
        // Simulate fetch operation
        setTimeout(() => {
            this.showNotification('Fetch completed', 'success');
        }, 1000);
    }

    pullChanges() {
        this.showNotification('Pulling changes...', 'info');
        // Simulate pull operation
        setTimeout(() => {
            this.showNotification('Pull completed', 'success');
        }, 1000);
    }

    pushChanges() {
        this.showNotification('Pushing changes...', 'info');
        // Simulate push operation
        setTimeout(() => {
            this.showNotification('Push completed', 'success');
        }, 1000);
    }

    syncChanges() {
        this.showNotification('Syncing changes...', 'info');
        // Simulate sync operation (pull + push)
        setTimeout(() => {
            this.showNotification('Sync completed', 'success');
        }, 1500);
    }

    updateRemoteList() {
        const container = this.container.querySelector('#remote-list');
        container.innerHTML = '';

        if (this.remotes.length === 0) {
            container.innerHTML = '<div class="empty-state">No remotes configured</div>';
            return;
        }

        this.remotes.forEach(remote => {
            const item = document.createElement('div');
            item.className = 'remote-list-item';

            item.innerHTML = `
                <div class="remote-info">
                    <span class="remote-name">${remote.name}</span>
                    <span class="remote-url">${remote.url}</span>
                </div>
                <div class="remote-actions">
                    <button class="remote-action-btn remove-btn" title="Remove">
                        ${createIcon('trash', 12)}
                    </button>
                </div>
            `;

            const removeBtn = item.querySelector('.remove-btn');
            removeBtn.addEventListener('click', () => {
                this.removeRemote(remote.name);
            });

            container.appendChild(item);
        });
    }

    removeRemote(remoteName) {
        if (confirm(`Are you sure you want to remove remote "${remoteName}"?`)) {
            this.remotes = this.remotes.filter(r => r.name !== remoteName);
            this.updateUI();
            this.saveGitState();
            this.showNotification(`Removed remote: ${remoteName}`, 'info');
        }
    }

    // History operations
    updateCommitHistory() {
        const container = this.container.querySelector('#commit-history');
        container.innerHTML = '';

        if (this.commits.length === 0) {
            container.innerHTML = '<div class="empty-state">No commits yet</div>';
            return;
        }

        this.commits.forEach(commit => {
            const item = document.createElement('div');
            item.className = 'commit-item';

            const date = new Date(commit.date);
            const timeAgo = this.getTimeAgo(date);

            item.innerHTML = `
                <div class="commit-info">
                    <div class="commit-message">${commit.message}</div>
                    <div class="commit-meta">
                        <span class="commit-id">${commit.id}</span>
                        <span class="commit-author">${commit.author}</span>
                        <span class="commit-date">${timeAgo}</span>
                    </div>
                </div>
                <div class="commit-actions">
                    <button class="commit-action-btn view-btn" title="View Changes">
                        ${createIcon('eye', 12)}
                    </button>
                </div>
            `;

            const viewBtn = item.querySelector('.view-btn');
            viewBtn.addEventListener('click', () => {
                this.viewCommit(commit);
            });

            container.appendChild(item);
        });
    }

    getTimeAgo(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'just now';
        if (minutes < 60) return `${minutes} minutes ago`;
        if (hours < 24) return `${hours} hours ago`;
        return `${days} days ago`;
    }

    viewCommit(commit) {
        // Show commit details
        alert(`Commit: ${commit.message}\nFiles: ${commit.files.join(', ')}\nAuthor: ${commit.author}`);
    }

    // Diff viewer
    showFileDiff(filePath) {
        const diffViewer = this.container.querySelector('#diff-viewer');
        const fileName = this.container.querySelector('#diff-file-name');
        const diffContent = this.container.querySelector('#diff-content');

        fileName.textContent = filePath;
        diffContent.innerHTML = this.generateMockDiff(filePath);

        diffViewer.classList.remove('hidden');
    }

    generateMockDiff(filePath) {
        // Generate a mock diff for demonstration
        return `
            <div class="diff-line removed">- const oldFunction = () => {</div>
            <div class="diff-line removed">-     console.log('old implementation');</div>
            <div class="diff-line removed">- };</div>
            <div class="diff-line added">+ const newFunction = () => {</div>
            <div class="diff-line added">+     console.log('new implementation');</div>
            <div class="diff-line added">+     return true;</div>
            <div class="diff-line added">+ };</div>
            <div class="diff-line unchanged">  </div>
            <div class="diff-line unchanged">  export default newFunction;</div>
        `;
    }

    closeDiffViewer() {
        this.container.querySelector('#diff-viewer').classList.add('hidden');
    }

    // Tab management
    switchTab(tabName) {
        // Update tab buttons
        this.container.querySelectorAll('.git-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        this.container.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab panels
        this.container.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        this.container.querySelector(`#${tabName}-panel`).classList.add('active');

        // Load tab-specific data
        switch (tabName) {
            case 'history':
                this.updateCommitHistory();
                break;
            case 'branches':
                this.updateBranchList();
                break;
            case 'remotes':
                this.updateRemoteList();
                break;
        }
    }

    toggleSection(sectionName) {
        const section = this.container.querySelector(`#${sectionName}-section`);
        const toggle = section.querySelector('.section-toggle');
        const content = section.querySelector('.section-content');

        content.classList.toggle('collapsed');

        if (content.classList.contains('collapsed')) {
            toggle.innerHTML = createIcon('chevronRight', 12);
        } else {
            toggle.innerHTML = createIcon('chevronDown', 12);
        }
    }

    // Settings
    showSettings() {
        const modal = this.container.querySelector('#git-settings-modal');
        modal.classList.remove('hidden');

        // Load current settings
        this.container.querySelector('#git-user-name').value = localStorage.getItem('git-user-name') || '';
        this.container.querySelector('#git-user-email').value = localStorage.getItem('git-user-email') || '';
        this.container.querySelector('#git-default-branch').value = localStorage.getItem('git-default-branch') || 'main';
        this.container.querySelector('#git-auto-fetch').checked = localStorage.getItem('git-auto-fetch') === 'true';
        this.container.querySelector('#git-auto-stage').checked = localStorage.getItem('git-auto-stage') === 'true';
    }

    hideSettings() {
        this.container.querySelector('#git-settings-modal').classList.add('hidden');
    }

    saveSettings() {
        const userName = this.container.querySelector('#git-user-name').value;
        const userEmail = this.container.querySelector('#git-user-email').value;
        const defaultBranch = this.container.querySelector('#git-default-branch').value;
        const autoFetch = this.container.querySelector('#git-auto-fetch').checked;
        const autoStage = this.container.querySelector('#git-auto-stage').checked;

        localStorage.setItem('git-user-name', userName);
        localStorage.setItem('git-user-email', userEmail);
        localStorage.setItem('git-default-branch', defaultBranch);
        localStorage.setItem('git-auto-fetch', autoFetch);
        localStorage.setItem('git-auto-stage', autoStage);

        this.hideSettings();
        this.showNotification('Git settings saved', 'success');
    }

    showNotification(message, type = 'info') {
        // Show notification in status bar or create a toast
        console.log(`[Git ${type.toUpperCase()}] ${message}`);

        // If there's a status bar available, use it
        if (window.rustCodeApp && window.rustCodeApp.statusBar) {
            window.rustCodeApp.statusBar.showNotification(message, type);
        }
    }

    // Enhanced methods for advanced Git integration
    loadSettings() {
        const savedSettings = localStorage.getItem('git-integration-settings');
        if (savedSettings) {
            try {
                const parsed = JSON.parse(savedSettings);
                this.settings = { ...this.settings, ...parsed };
            } catch (error) {
                console.warn('Failed to load Git integration settings:', error);
            }
        }
    }

    saveSettingsToStorage() {
        localStorage.setItem('git-integration-settings', JSON.stringify(this.settings));
    }

    loadGitConfig() {
        // Load Git configuration
        const savedConfig = localStorage.getItem('git-config');
        if (savedConfig) {
            try {
                const parsed = JSON.parse(savedConfig);
                this.gitConfig = { ...this.gitConfig, ...parsed };
            } catch (error) {
                console.warn('Failed to load Git config:', error);
            }
        }
    }

    saveGitConfig() {
        localStorage.setItem('git-config', JSON.stringify(this.gitConfig));
    }

    initializeRepository() {
        // Initialize repository information
        this.detectRepository();
        this.loadRepositoryInfo();
        this.initializeGitHooks();
    }

    detectRepository() {
        // Simulate repository detection
        this.repositoryInfo.isRepository = true;
        this.repositoryInfo.hasRemote = this.remotes.length > 0;

        if (!this.repositoryInfo.hasRemote) {
            // Add default remote for demo
            this.remotes.push({
                name: 'origin',
                url: 'https://github.com/user/repo.git',
                type: 'fetch'
            });
            this.repositoryInfo.hasRemote = true;
        }
    }

    loadRepositoryInfo() {
        // Load repository statistics
        this.repositoryInfo.totalCommits = this.commits.length;
        this.repositoryInfo.lastCommit = this.commits.length > 0 ? this.commits[0] : null;

        // Simulate ahead/behind status
        this.repositoryInfo.ahead = Math.floor(Math.random() * 3);
        this.repositoryInfo.behind = Math.floor(Math.random() * 2);
    }

    initializeGitHooks() {
        if (!this.gitConfig.enableGitHooks) return;

        // Simulate Git hooks setup
        this.gitHooks = {
            'pre-commit': this.preCommitHook.bind(this),
            'post-commit': this.postCommitHook.bind(this),
            'pre-push': this.prePushHook.bind(this),
            'post-merge': this.postMergeHook.bind(this)
        };
    }

    preCommitHook() {
        // Pre-commit validation
        if (this.gitConfig.autoStage) {
            this.stageAllFiles();
        }

        // Validate commit message
        const commitMessage = this.container.querySelector('#commit-message').value.trim();
        if (commitMessage.length < 10) {
            this.showNotification('Commit message too short (minimum 10 characters)', 'warning');
            return false;
        }

        return true;
    }

    postCommitHook() {
        // Post-commit actions
        this.recordOperation('commit', 'success');
        this.updateRepositoryInfo();
    }

    prePushHook() {
        // Pre-push validation
        if (this.stagedFiles.size === 0 && this.modifiedFiles.size > 0) {
            this.showNotification('You have unstaged changes. Stage them before pushing.', 'warning');
            return false;
        }
        return true;
    }

    postMergeHook() {
        // Post-merge actions
        this.refreshGitStatus();
        this.updateCommitHistory();
    }

    startAutoRefresh() {
        if (!this.settings.enableRealTimeSync) return;

        this.refreshTimer = setInterval(() => {
            this.refreshGitStatus();
        }, this.settings.autoRefreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    recordOperation(operation, status, details = null) {
        const record = {
            operation,
            status,
            timestamp: new Date(),
            details,
            duration: this.performanceMetrics.refreshDuration
        };

        this.performanceMetrics.operationHistory.push(record);

        // Keep only recent operations
        if (this.performanceMetrics.operationHistory.length > 50) {
            this.performanceMetrics.operationHistory = this.performanceMetrics.operationHistory.slice(-25);
        }
    }

    updateRepositoryInfo() {
        this.repositoryInfo.totalCommits = this.commits.length;
        this.repositoryInfo.lastCommit = this.commits.length > 0 ? this.commits[0] : null;
    }

    // Advanced Git operations
    async createStash(message = null) {
        const stashMessage = message || `WIP on ${this.currentBranch}: ${new Date().toISOString()}`;

        const stash = {
            id: `stash@{${this.stashList.length}}`,
            message: stashMessage,
            branch: this.currentBranch,
            timestamp: new Date(),
            files: Array.from(this.modifiedFiles)
        };

        this.stashList.unshift(stash);

        // Clear working directory changes
        this.modifiedFiles.clear();
        this.updateUI();

        this.showNotification(`Created stash: ${stashMessage}`, 'success');
        this.recordOperation('stash', 'success', { message: stashMessage });
    }

    async applyStash(stashId) {
        const stashIndex = this.stashList.findIndex(s => s.id === stashId);
        if (stashIndex === -1) {
            this.showNotification('Stash not found', 'error');
            return;
        }

        const stash = this.stashList[stashIndex];

        // Apply stash changes
        stash.files.forEach(file => {
            this.modifiedFiles.add(file);
        });

        this.updateUI();
        this.showNotification(`Applied stash: ${stash.message}`, 'success');
        this.recordOperation('stash-apply', 'success', { stashId });
    }

    async dropStash(stashId) {
        const stashIndex = this.stashList.findIndex(s => s.id === stashId);
        if (stashIndex === -1) {
            this.showNotification('Stash not found', 'error');
            return;
        }

        const stash = this.stashList.splice(stashIndex, 1)[0];
        this.showNotification(`Dropped stash: ${stash.message}`, 'success');
        this.recordOperation('stash-drop', 'success', { stashId });
    }

    async createTag(tagName, message = null) {
        if (this.tags.find(t => t.name === tagName)) {
            this.showNotification(`Tag '${tagName}' already exists`, 'error');
            return;
        }

        const tag = {
            name: tagName,
            message: message || `Tag ${tagName}`,
            commit: this.repositoryInfo.lastCommit?.hash || 'HEAD',
            timestamp: new Date(),
            tagger: this.gitConfig.userName || 'Unknown'
        };

        this.tags.push(tag);
        this.showNotification(`Created tag: ${tagName}`, 'success');
        this.recordOperation('tag', 'success', { tagName });
    }

    async deleteTag(tagName) {
        const tagIndex = this.tags.findIndex(t => t.name === tagName);
        if (tagIndex === -1) {
            this.showNotification('Tag not found', 'error');
            return;
        }

        this.tags.splice(tagIndex, 1);
        this.showNotification(`Deleted tag: ${tagName}`, 'success');
        this.recordOperation('tag-delete', 'success', { tagName });
    }

    // Enhanced file operations
    async getFileHistory(filePath) {
        // Simulate file history
        const history = [];
        for (let i = 0; i < 10; i++) {
            history.push({
                commit: `commit-${i}`,
                author: this.gitConfig.userName || 'Developer',
                date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
                message: `Update ${filePath}`,
                changes: Math.floor(Math.random() * 50) + 1
            });
        }
        return history;
    }

    async getFileBlame(filePath) {
        // Simulate file blame information
        const blame = [];
        const lines = 50; // Simulate 50 lines

        for (let i = 1; i <= lines; i++) {
            blame.push({
                line: i,
                commit: `commit-${Math.floor(Math.random() * 10)}`,
                author: this.gitConfig.userName || 'Developer',
                date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
                content: `Line ${i} content`
            });
        }

        return blame;
    }

    async cherryPickCommit(commitHash) {
        // Simulate cherry-pick operation
        const commit = this.commits.find(c => c.hash === commitHash);
        if (!commit) {
            this.showNotification('Commit not found', 'error');
            return;
        }

        // Create new commit based on cherry-picked commit
        const newCommit = {
            ...commit,
            hash: `cherry-${Date.now()}`,
            timestamp: new Date(),
            message: `${commit.message} (cherry picked from commit ${commitHash.substring(0, 7)})`
        };

        this.commits.unshift(newCommit);
        this.updateCommitHistory();

        this.showNotification(`Cherry-picked commit ${commitHash.substring(0, 7)}`, 'success');
        this.recordOperation('cherry-pick', 'success', { commitHash });
    }

    async revertCommit(commitHash) {
        // Simulate revert operation
        const commit = this.commits.find(c => c.hash === commitHash);
        if (!commit) {
            this.showNotification('Commit not found', 'error');
            return;
        }

        const revertCommit = {
            hash: `revert-${Date.now()}`,
            author: this.gitConfig.userName || 'Developer',
            timestamp: new Date(),
            message: `Revert "${commit.message}"\n\nThis reverts commit ${commitHash}.`,
            files: commit.files || []
        };

        this.commits.unshift(revertCommit);
        this.updateCommitHistory();

        this.showNotification(`Reverted commit ${commitHash.substring(0, 7)}`, 'success');
        this.recordOperation('revert', 'success', { commitHash });
    }

    // Enhanced cleanup and disposal
    dispose() {
        // Stop auto-refresh
        this.stopAutoRefresh();

        // Clear event listeners
        this.eventListeners.forEach(listener => {
            if (listener.element && listener.event && listener.handler) {
                listener.element.removeEventListener(listener.event, listener.handler);
            }
        });
        this.eventListeners.length = 0;

        // Save state before disposal
        this.saveGitState();
        this.saveGitConfig();
        this.saveSettingsToStorage();

        console.log('Git Integration disposed successfully');
    }
}
